{"version": "5.0", "deps": {"@azure/msal-node": "3.6.2", "@convergence/jwt-util": "^0.2.0", "@dropbox/sign": "^1.3.0", "@google-cloud/local-auth": "^2.1.0", "@google-cloud/talent": "^4.1.1", "@langchain/core": "^0.3.57", "@langchain/langgraph": "^0.3.0", "@langchain/openai": "^0.0.28", "@microsoft/microsoft-graph-client": "^3.0.4", "@nestjs/axios": "^3.0.0", "@nestjs/common": "^8.0.0", "@nestjs/config": "^1.1.6", "@nestjs/core": "^8.0.0", "@nestjs/microservices": "^8.2.4", "@nestjs/passport": "^9.0.0", "@nestjs/platform-express": "^8.0.0", "@nestjs/platform-socket.io": "^8.2.5", "@nestjs/sequelize": "^8.0.0", "@nestjs/swagger": "^5.2.0", "@nestjs/websockets": "^8.2.5", "@opensearch-project/opensearch": "^2.5.0", "@qdrant/qdrant-js": "^1.13.0", "@sendgrid/mail": "^7.6.0", "@temporalio/activity": "^1.11.2", "@temporalio/client": "^1.11.2", "@temporalio/common": "^1.11.2", "@temporalio/worker": "^1.11.2", "@temporalio/workflow": "^1.11.2", "@types/archiver": "^5.3.1", "@types/base-64": "^1.0.0", "@types/multer": "^1.4.7", "archiver": "^5.3.1", "aws-sdk": "2.1059.0", "base-64": "^1.0.0", "bcrypt": "^5.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "cross-fetch": "^3.1.5", "dotenv": "^16.4.7", "express-jwt": "^6.1.0", "firebase": "^10.12.2", "firebase-admin": "^12.1.1", "googleapis": "^107.0.0", "isomorphic-fetch": "^3.0.0", "jwks-rsa": "^2.0.5", "langchain": "^0.1.36", "langgraph": "langchain-ai/langgraph", "moment": "^2.29.4", "multer": "1.4.4", "multer-s3": "2.10.0", "mysql2": "^2.3.3", "nestjs-stripe": "1.0.0", "nodemailer": "^6.7.2", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "pdf-parse": "^1.1.1", "pg": "^8.11.5", "puppeteer": "^22.6.2", "redis": "^3", "reflect-metadata": "^0.1.13", "rxjs": "^7.0.0", "sequelize": "^6.25.3", "sequelize-typescript": "^2.1.5", "sqlite3": "^5.1.1", "stripe": "^8.222.0", "swagger-ui-express": "^4.3.0", "ts-node": "^10.9.2", "tslib": "^2.0.0", "twilio": "^3.81.0", "undici": "^5.5.1", "uuid": "^9.0.0", "zod": "^3.23.5", "@macpaw/eslint-config-base": "^2.0.1", "@macpaw/eslint-config-typescript": "^2.0.1", "@microsoft/microsoft-graph-types": "^2.25.0", "@nestjs/schematics": "^8.0.0", "@nestjs/testing": "^8.0.0", "@nrwl/cli": "13.4.3", "@nrwl/eslint-plugin-nx": "13.4.3", "@nrwl/jest": "13.4.3", "@nrwl/linter": "13.4.3", "@nrwl/nest": "13.4.3", "@nrwl/node": "13.4.3", "@nrwl/tao": "13.4.3", "@nrwl/workspace": "13.4.3", "@types/jest": "27.0.2", "@types/node": "14.14.33", "@types/nodemailer": "^6.4.4", "@types/passport-jwt": "^3.0.6", "@types/sequelize": "^4.28.14", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "eslint": "8.57.1", "eslint-config-prettier": "8.1.0", "husky": "^7.0.4", "jest": "30.0.4", "lint-staged": "^12.3.1", "prettier": "^2.3.1", "ts-jest": "27.0.5", "typescript": "^5.6.3"}, "pathMappings": {"@microservices/auth": ["libs/auth/src/index.ts"], "@microservices/calendars": ["libs/calendars/src/index.ts"], "@microservices/code-executor": ["libs/code-executor/src/index.ts"], "@microservices/db": ["libs/db/src/index.ts"], "@microservices/editor-manager": ["libs/editor-manager/src/index.ts"], "@microservices/email": ["libs/email/src/index.ts"], "@microservices/hr-analytics": ["libs/hr-analytics/src/index.ts"], "@microservices/integrations": ["libs/integrations/src/index.ts"], "@microservices/qdrant": ["libs/qdrant/src/index.ts"], "@microservices/kinesis": ["libs/kinesis/src/index.ts"], "@microservices/recruitment-db": ["libs/recruitment-db/src/index.ts"], "@microservices/twilio": ["libs/twilio/src/index.ts"]}, "nxJsonPlugins": [], "nodes": {"pre-recruitment-api": {"name": "pre-recruitment-api", "type": "app", "data": {"root": "apps/pre-recruitment-api", "sourceRoot": "apps/pre-recruitment-api/src", "projectType": "application", "targets": {"build": {"executor": "@nrwl/node:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/pre-recruitment-api", "main": "apps/pre-recruitment-api/src/main.ts", "tsConfig": "apps/pre-recruitment-api/tsconfig.app.json", "assets": ["apps/pre-recruitment-api/src/assets"], "generatePackageJson": true}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/pre-recruitment-api/src/environments/environment.ts", "with": "apps/pre-recruitment-api/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nrwl/node:execute", "options": {"buildTarget": "pre-recruitment-api:build"}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/pre-recruitment-api/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/apps/pre-recruitment-api"], "options": {"jestConfig": "apps/pre-recruitment-api/jest.config.js", "passWithNoTests": true}}}, "tags": [], "files": [{"file": "apps/pre-recruitment-api/.eslintrc.json", "hash": "9d9c0db55bb1e91c5f2e7b64a02bc6bf69fc7cb5"}, {"file": "apps/pre-recruitment-api/jest.config.js", "hash": "6f014a123ef5d0c59a5ab9d2ea5b63a93d034495"}, {"file": "apps/pre-recruitment-api/project.json", "hash": "9caed6ac6e995a3731db0bf2c813cb9309dfb656"}, {"file": "apps/pre-recruitment-api/src/app.module.ts", "hash": "ed6264dcf7b8fb91bd10f568a31059368b3dd010", "deps": ["npm:@nestjs/common", "npm:@nestjs/microservices", "recruitment-db", "integrations", "auth", "email", "calendars", "twi<PERSON>", "db", "npm:@nestjs/axios", "hr-analytics"]}, {"file": "apps/pre-recruitment-api/src/assets/.gitkeep", "hash": "e69de29bb2d1d6434b8b29ae775ad8c2e48c5391"}, {"file": "apps/pre-recruitment-api/src/assets/templates/activateAccount.html", "hash": "6e185b16eec6b624a7be8adad402974ea6c98f28"}, {"file": "apps/pre-recruitment-api/src/assets/templates/approval.html", "hash": "6cdd7746f9e78ef9bf6da8ba3b36db419bc1b486"}, {"file": "apps/pre-recruitment-api/src/assets/templates/contactForm.html", "hash": "8c9fbdc3b54bb5d6827fd76e869f4998045a775f"}, {"file": "apps/pre-recruitment-api/src/assets/templates/jobDetail.html", "hash": "96f40f6860ac4cf4eee9115b4bfbc80f70efe6a3"}, {"file": "apps/pre-recruitment-api/src/assets/templates/mailTemplate.html", "hash": "75876a457130480b620c972b3221ff80db5c369f"}, {"file": "apps/pre-recruitment-api/src/assets/templates/trialEnds.html", "hash": "10a98f22d8ef863b6f78d16e2960b600b2b18ffc"}, {"file": "apps/pre-recruitment-api/src/assets/templates/welcome.html", "hash": "0764cec417c862dc1218c89792c73ea394ace2aa"}, {"file": "apps/pre-recruitment-api/src/background-screening/background.controller.ts", "hash": "d00cfa8bfe32257f8208810a0f6875124a44e7c0", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "auth", "npm:@nestjs/passport", "recruitment-db"]}, {"file": "apps/pre-recruitment-api/src/calendar/calendar.controller.ts", "hash": "49fad404946f4c71307c3f897a48e580ee74e865", "deps": ["auth", "calendars", "recruitment-db", "npm:@nestjs/common", "npm:@nestjs/passport", "npm:@nestjs/swagger", "npm:@nestjs/sequelize", "temporal", "email", "npm:@nestjs/axios"]}, {"file": "apps/pre-recruitment-api/src/calendar/mcal.controller.ts", "hash": "dd9d6bbf374179a12ab4bd3c058a92d5966b0a59", "deps": ["auth", "calendars", "recruitment-db", "npm:@nestjs/common", "npm:@nestjs/passport", "npm:@nestjs/swagger", "temporal"]}, {"file": "apps/pre-recruitment-api/src/candidate/candidates.controller.ts", "hash": "d73a3f412cb0cbfece3af42b47d0ab0b8120fd3e", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "auth", "npm:@nestjs/passport", "recruitment-db", "npm:@nestjs/platform-express"]}, {"file": "apps/pre-recruitment-api/src/candidateoffers/candidate-offers.controller.ts", "hash": "80c71e932f535ab0fae9f89c5eddf5c424c270ff", "deps": ["auth", "recruitment-db", "npm:@nestjs/common", "npm:@nestjs/passport", "npm:@nestjs/swagger"]}, {"file": "apps/pre-recruitment-api/src/company/company.controller.ts", "hash": "3dfd6ad4e76a4873367aa09d0f84a0b505415ae8", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "auth", "npm:@nestjs/passport", "recruitment-db"]}, {"file": "apps/pre-recruitment-api/src/company/dto/delete-user.dto.ts", "hash": "4beadfd3c18b9ce3d65c727a9ddae66f62ecd9ef", "deps": ["npm:@nestjs/swagger"]}, {"file": "apps/pre-recruitment-api/src/department/departments.controller.ts", "hash": "27aea54937ec50f0931e7b943c4f61287c3b3f60", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "auth", "npm:@nestjs/passport", "recruitment-db"]}, {"file": "apps/pre-recruitment-api/src/department/dto/department.dto.ts", "hash": "3858212585b43b1d20c84c7a1d13f0b009899aff", "deps": ["npm:@nestjs/swagger"]}, {"file": "apps/pre-recruitment-api/src/drug-screening/drug.controller.ts", "hash": "34578869aff46e7c6218adbf458a761284f97577", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "auth", "npm:@nestjs/passport", "recruitment-db"]}, {"file": "apps/pre-recruitment-api/src/email/dto/email.dto.ts", "hash": "30238560b0c271d20d2b4871d58b8d7e98509311", "deps": ["npm:@nestjs/swagger"]}, {"file": "apps/pre-recruitment-api/src/email/email.controller.ts", "hash": "a18998089a0b76326da80d81efb18d60afa0efc2", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "email", "recruitment-db", "npm:@nestjs/axios"]}, {"file": "apps/pre-recruitment-api/src/file/file.controller.ts", "hash": "c46c8b7c3536e9ababc830c1aabce0834d0bcb08", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "npm:@nestjs/passport"]}, {"file": "apps/pre-recruitment-api/src/file/file.module.ts", "hash": "ed9a823d142f902fa15ee33852e223e9951e164b", "deps": ["npm:@nestjs/common", "integrations"]}, {"file": "apps/pre-recruitment-api/src/file/file.service.ts", "hash": "39f15c32fe5a13ffe4c67d9e0c0a0830a3a8c5d9", "deps": ["npm:@nestjs/common", "npm:multer", "npm:aws-sdk", "npm:multer-s3"]}, {"file": "apps/pre-recruitment-api/src/freshsales/freshsales.controller.ts", "hash": "5dce1af9c182d9dbdc61a17281d4c7eae5d4a5c0", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "recruitment-db"]}, {"file": "apps/pre-recruitment-api/src/gmail/gmail.controller.ts", "hash": "d0960de398c0e45ef3102f763f73eb06120152c5", "deps": ["auth", "recruitment-db", "npm:@nestjs/common", "npm:@nestjs/passport", "npm:@nestjs/platform-express", "npm:@nestjs/swagger"]}, {"file": "apps/pre-recruitment-api/src/hellosign/hellosign-integration.controller.ts", "hash": "78759b57960bf38248ef967ef9e555290abd117f", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "auth", "npm:@nestjs/passport", "npm:@nestjs/platform-express", "recruitment-db"]}, {"file": "apps/pre-recruitment-api/src/industry/dto/industry.dto.ts", "hash": "814e2b315cc3ea8913cf0faa93ad1193434b1862", "deps": ["npm:@nestjs/swagger"]}, {"file": "apps/pre-recruitment-api/src/industry/industries.controller.ts", "hash": "9fd0ce62c110ac6598c92315d61136a10a7d569f", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "recruitment-db", "npm:@nestjs/passport", "auth"]}, {"file": "apps/pre-recruitment-api/src/integrations/integrations.controller.ts", "hash": "397f2a887577bf57b6c0625ef9ff4d0f27872885", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "auth", "npm:@nestjs/passport", "recruitment-db"]}, {"file": "apps/pre-recruitment-api/src/interviews/interviews.controller.ts", "hash": "b37e83a681a1c494c22fc5916d8b96d1614f72a7", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "auth", "npm:@nestjs/passport", "recruitment-db"]}, {"file": "apps/pre-recruitment-api/src/job/job.controller.ts", "hash": "17264ba5c11f507d02c5dec122a17d197acbd177", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "auth", "npm:@nestjs/passport", "recruitment-db"]}, {"file": "apps/pre-recruitment-api/src/jobtarget/jobtarget.controller.ts", "hash": "4d39148174eeb658400a20988ef61723e36f168b", "deps": ["npm:@nestjs/common", "recruitment-db", "auth", "npm:@nestjs/passport", "npm:@nestjs/swagger"]}, {"file": "apps/pre-recruitment-api/src/langgraph/langgraph.controller.ts", "hash": "a4e52fd12a544002a8a299bc9d3a92912d784b3c", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "npm:@nestjs/passport", "auth"]}, {"file": "apps/pre-recruitment-api/src/langgraph/langgraph.module.ts", "hash": "f591543722b933f2bbf633458137d69644ea912e", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:@nestjs/axios", "recruitment-db", "integrations"]}, {"file": "apps/pre-recruitment-api/src/langgraph/langgraph.nodes.ts", "hash": "9ecfd4e47d8b2189b1907cb8d433d186e49ecf16", "deps": ["recruitment-db", "npm:@langchain/openai", "npm:langchain"]}, {"file": "apps/pre-recruitment-api/src/langgraph/langgraph.service.ts", "hash": "f7707e583ace26663e9290c43e3e34ec521af9ac", "deps": ["npm:@nestjs/common", "npm:@langchain/langgraph", "recruitment-db"]}, {"file": "apps/pre-recruitment-api/src/location/locations.controller.ts", "hash": "46fcbd8ce8a0c4798e73dd9c1516f9b5446bce9d", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "recruitment-db", "npm:@nestjs/passport", "auth"]}, {"file": "apps/pre-recruitment-api/src/main.ts", "hash": "30caffdf3014385d63de3d229c5d4cabfcb3a29b", "deps": ["npm:@nestjs/common", "npm:@nestjs/core", "npm:@nestjs/microservices", "npm:@nestjs/swagger", "qdrant", "kinesis"]}, {"file": "apps/pre-recruitment-api/src/notifications/notifications.controller.ts", "hash": "c577b80d9e06af30d69eaf8d5cc943dd804d4cd6", "deps": ["auth", "recruitment-db", "npm:@nestjs/common", "npm:@nestjs/passport", "npm:@nestjs/swagger"]}, {"file": "apps/pre-recruitment-api/src/notify-permissions/notify-permissions.controller.ts", "hash": "a38e9e80d94274c1645167b1f17ad3502a7ea641", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "recruitment-db", "npm:@nestjs/passport", "auth"]}, {"file": "apps/pre-recruitment-api/src/offer/offer.controller.ts", "hash": "6705342420cba14b197e567eb2c7193345eb0647", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "auth", "npm:@nestjs/passport", "recruitment-db", "npm:@nestjs/platform-express"]}, {"file": "apps/pre-recruitment-api/src/outlook/outlook.controller.ts", "hash": "b5fe96a5bad76c6fe13f88294dcc659a75e967e4", "deps": ["auth", "recruitment-db", "npm:@nestjs/common", "npm:@nestjs/passport", "npm:@nestjs/platform-express", "npm:@nestjs/swagger"]}, {"file": "apps/pre-recruitment-api/src/payment/payment.controller.ts", "hash": "3c2ccbd449845030f8fcd74992c2468f1b5a26bc", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "npm:@nestjs/passport", "auth", "recruitment-db"]}, {"file": "apps/pre-recruitment-api/src/playback/playback.controller.ts", "hash": "7fbf282cb98ca9efc173f7e3ca0798b353c9599f", "deps": ["auth", "recruitment-db", "npm:@nestjs/common", "npm:@nestjs/passport", "npm:@nestjs/swagger"]}, {"file": "apps/pre-recruitment-api/src/position/position.controller.ts", "hash": "43015b58ac6608425be7fd672fdf2ec2dbe654ff", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "recruitment-db", "npm:@nestjs/passport", "auth"]}, {"file": "apps/pre-recruitment-api/src/product/product.controller.ts", "hash": "78c3734e66b072bf3f90edca8a82be581dc78314", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "npm:@nestjs/passport", "auth", "recruitment-db"]}, {"file": "apps/pre-recruitment-api/src/recruiter-position/recruiter-position.controller.ts", "hash": "95b9c21f4d54a2f672b67372c6336ea003a56139", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "recruitment-db", "auth", "npm:@nestjs/passport"]}, {"file": "apps/pre-recruitment-api/src/recruiter/recruiter.controller.ts", "hash": "23f8241437a183ab0297c75fe8b38293db4dae71", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "auth", "npm:@nestjs/passport", "recruitment-db"]}, {"file": "apps/pre-recruitment-api/src/round/round.controller.ts", "hash": "e702503c47f1f11abb7bb4582b70329121f7359e", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "recruitment-db", "npm:@nestjs/passport", "auth"]}, {"file": "apps/pre-recruitment-api/src/seed/seed.controller.ts", "hash": "5dbf4977109a1bed92b84937d470732ea140dfaa", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "recruitment-db"]}, {"file": "apps/pre-recruitment-api/src/skills/skills.controller.ts", "hash": "41d525756f6f89feddfdeb51dcaff77a60415370", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "recruitment-db", "npm:@nestjs/passport", "auth"]}, {"file": "apps/pre-recruitment-api/src/subscribe/subscribe.controller.ts", "hash": "278aaf7945ddbec1d4554e8c624e9541bc1d1ae9", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "recruitment-db", "npm:@nestjs/passport", "auth", "npm:@nestjs/axios", "temporal", "npm:uuid"]}, {"file": "apps/pre-recruitment-api/src/subscription/subscription.controller.ts", "hash": "17d2c84c0f9bfdfdd369127ebba00cd9c3e16cde", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "npm:@nestjs/passport", "auth", "recruitment-db"]}, {"file": "apps/pre-recruitment-api/src/temporal-workflow/temporal-workflow.controller.ts", "hash": "0fbbeb297af79c011a2cd030040feec5d9ec7bc0", "deps": ["auth", "recruitment-db", "npm:@nestjs/common", "npm:@nestjs/passport", "npm:@nestjs/swagger"]}, {"file": "apps/pre-recruitment-api/src/twilio/twilio.controller.ts", "hash": "1ecb99887da7a62737e9a8b5575282fe19541784", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "twi<PERSON>", "auth", "npm:@nestjs/passport"]}, {"file": "apps/pre-recruitment-api/src/universal/universal.controller.ts", "hash": "60380fb127f32067fd761c14a0df547ef03747d2", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "auth", "npm:@nestjs/passport", "recruitment-db"]}, {"file": "apps/pre-recruitment-api/src/user-assignments/user-assignments.controller.ts", "hash": "dfe1a6d91e2ea295193288b215f6fc98af596323", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "npm:@nestjs/passport", "auth", "recruitment-db"]}, {"file": "apps/pre-recruitment-api/src/user/dto/change-password.dto.ts", "hash": "c5b9f136c62e0f0c059e8500cbd756a30a2100ff", "deps": ["npm:@nestjs/swagger"]}, {"file": "apps/pre-recruitment-api/src/user/users.controller.ts", "hash": "38aa93f07e104f4f594520d77d694eef0dbf3c1e", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "recruitment-db", "npm:@nestjs/passport", "auth"]}, {"file": "apps/pre-recruitment-api/src/workflow/workflow.controller.ts", "hash": "5982c1508b45040a64d80dd40df5c05bd06901c1", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "auth", "npm:@nestjs/passport", "recruitment-db"]}, {"file": "apps/pre-recruitment-api/tsconfig.app.json", "hash": "515cb2d76f75cf59bd88a173bd9e3a336d6a6846"}, {"file": "apps/pre-recruitment-api/tsconfig.json", "hash": "63dbe35fb282d5f9ac4a724607173e6316269e29"}, {"file": "apps/pre-recruitment-api/tsconfig.spec.json", "hash": "a18afb604688956c6a84dd7780d99923f0c04662"}]}}, "hr-analytics-api": {"name": "hr-analytics-api", "type": "app", "data": {"root": "apps/hr-analytics-api", "sourceRoot": "apps/hr-analytics-api/src", "projectType": "application", "targets": {"build": {"executor": "@nrwl/node:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/hr-analytics-api", "main": "apps/hr-analytics-api/src/main.ts", "tsConfig": "apps/hr-analytics-api/tsconfig.app.json", "generatePackageJson": true}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/hr-analytics-api/src/environments/environment.ts", "with": "apps/hr-analytics-api/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nrwl/node:execute", "options": {"buildTarget": "hr-analytics-api:build"}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/hr-analytics-api/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/apps/hr-analytics-api"], "options": {"jestConfig": "apps/hr-analytics-api/jest.config.js", "passWithNoTests": true}}}, "tags": [], "files": [{"file": "apps/hr-analytics-api/.eslintrc.json", "hash": "9d9c0db55bb1e91c5f2e7b64a02bc6bf69fc7cb5"}, {"file": "apps/hr-analytics-api/jest.config.js", "hash": "f694d16d95d099e8f295f1eabb8e9b2744c28a63"}, {"file": "apps/hr-analytics-api/project.json", "hash": "658ae42a4b45f08a1e1c68b79356f600260fbc55"}, {"file": "apps/hr-analytics-api/src/app.controler.ts", "hash": "52f39a8b75392ad9e2c8b5e3a1c80bacdbac1477", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger"]}, {"file": "apps/hr-analytics-api/src/app.module.ts", "hash": "58adcb733d29f9d310b01a028bd21019a4461534", "deps": ["npm:@nestjs/common", "hr-analytics", "npm:@nestjs/microservices", "auth"]}, {"file": "apps/hr-analytics-api/src/assessments-analytics/assessments-analytics.controller.ts", "hash": "2d108f836bdc781067865013649fe195bda6b3b2", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "auth", "npm:@nestjs/passport", "hr-analytics"]}, {"file": "apps/hr-analytics-api/src/assessments-analytics/dto/assessments-analytics.dto.ts", "hash": "bad9d2a92a6aa534d309e45b3361ced981b3ab5d", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "apps/hr-analytics-api/src/jobs-analytics/dto/jobs-analytics.dto.ts", "hash": "7ea3ae861c45a3bcd56bf68608efa8f98902c848", "deps": ["npm:class-validator", "npm:class-transformer", "npm:@nestjs/swagger", "hr-analytics"]}, {"file": "apps/hr-analytics-api/src/jobs-analytics/jobs-analytics.controller.ts", "hash": "b91a963eab6f9f7ab4fde30546358aad86a13358", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "auth", "npm:@nestjs/passport", "hr-analytics"]}, {"file": "apps/hr-analytics-api/src/main.ts", "hash": "602d954d1599a59d3747865b6d11fe432dfddc9d", "deps": ["npm:@nestjs/common", "npm:@nestjs/core", "npm:@nestjs/microservices", "npm:@nestjs/swagger"]}, {"file": "apps/hr-analytics-api/src/users-analytics/dto/members-analytics-query.dto.ts", "hash": "dda1c746124f644a5b534420c27495745fb6dd3a", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "apps/hr-analytics-api/src/users-analytics/users-analytics.controller.ts", "hash": "1107c4696ea62a38a8f9adcb38fff8b809ca208f", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "auth", "npm:@nestjs/passport", "hr-analytics"]}, {"file": "apps/hr-analytics-api/tsconfig.app.json", "hash": "515cb2d76f75cf59bd88a173bd9e3a336d6a6846"}, {"file": "apps/hr-analytics-api/tsconfig.json", "hash": "63dbe35fb282d5f9ac4a724607173e6316269e29"}, {"file": "apps/hr-analytics-api/tsconfig.spec.json", "hash": "a18afb604688956c6a84dd7780d99923f0c04662"}]}}, "integrations-api": {"name": "integrations-api", "type": "app", "data": {"root": "apps/integrations-api", "sourceRoot": "apps/integrations-api/src", "projectType": "application", "targets": {"build": {"executor": "@nrwl/node:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/integrations-api", "main": "apps/integrations-api/src/main.ts", "tsConfig": "apps/integrations-api/tsconfig.app.json", "assets": ["apps/integrations-api/src/assets"], "generatePackageJson": true}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/integrations-api/src/environments/environment.ts", "with": "apps/integrations-api/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nrwl/node:execute", "options": {"buildTarget": "integrations-api:build"}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/integrations-api/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/apps/integrations-api"], "options": {"jestConfig": "apps/integrations-api/jest.config.js", "passWithNoTests": true}}}, "tags": [], "files": [{"file": "apps/integrations-api/.eslintrc.json", "hash": "9d9c0db55bb1e91c5f2e7b64a02bc6bf69fc7cb5"}, {"file": "apps/integrations-api/jest.config.js", "hash": "4084eabcf2adbfbe1ffdf035516bc4f5eb920199"}, {"file": "apps/integrations-api/project.json", "hash": "cfac38f1e01b3730736a04e9e588ad7845ca54c9"}, {"file": "apps/integrations-api/src/app.module.ts", "hash": "777e8ab1759c82decbc482ae87c15165bb7228ee", "deps": ["npm:@nestjs/common", "npm:@nestjs/microservices", "email", "npm:@nestjs/axios"]}, {"file": "apps/integrations-api/src/assets/.gitkeep", "hash": "e69de29bb2d1d6434b8b29ae775ad8c2e48c5391"}, {"file": "apps/integrations-api/src/assets/templates/activateAccount.html", "hash": "6e185b16eec6b624a7be8adad402974ea6c98f28"}, {"file": "apps/integrations-api/src/assets/templates/approval.html", "hash": "6f0c9f1af91221b615531cfd0c6a2bc0de1c4a65"}, {"file": "apps/integrations-api/src/assets/templates/contactForm.html", "hash": "8c9fbdc3b54bb5d6827fd76e869f4998045a775f"}, {"file": "apps/integrations-api/src/assets/templates/jobDetail.html", "hash": "ab01932e5928848214ebee4c7e7723623f815ff3"}, {"file": "apps/integrations-api/src/assets/templates/welcome.html", "hash": "0764cec417c862dc1218c89792c73ea394ace2aa"}, {"file": "apps/integrations-api/src/dto/create-test-manager.dto.ts", "hash": "ea5ff28f6b644fc1391940d94637b03b082254e1", "deps": ["npm:@nestjs/swagger", "npm:class-validator", "npm:sequelize-typescript"]}, {"file": "apps/integrations-api/src/facebook/facebook.controller.spec.ts", "hash": "0d8152683a5dc377f71ff756d9f5548eb2cfe438", "deps": ["npm:@nestjs/testing"]}, {"file": "apps/integrations-api/src/facebook/facebook.controller.ts", "hash": "48bfc83bc5924292b419c786cad2a3cf50bb9d74", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger"]}, {"file": "apps/integrations-api/src/facebook/facebook.module.ts", "hash": "ba15b4457c4504ee5d299107c9c41a98bb7d5ccc", "deps": ["npm:@nestjs/common"]}, {"file": "apps/integrations-api/src/facebook/facebook.service.spec.ts", "hash": "e8d5e133d3bc370cbf7859a1e84d211c3abb70ad", "deps": ["npm:@nestjs/testing"]}, {"file": "apps/integrations-api/src/facebook/facebook.service.ts", "hash": "7bddba1010cda7bb260391217a2f16b82e7afa68", "deps": ["npm:@nestjs/common"]}, {"file": "apps/integrations-api/src/google/dto/job.dto.ts", "hash": "37bdbb775193a663eb7f543d86f3f447147c3d1c", "deps": ["npm:@nestjs/swagger"]}, {"file": "apps/integrations-api/src/google/google.controller.spec.ts", "hash": "a3e655e97376aebd649b6a945f9282a13a03797c", "deps": ["npm:@nestjs/testing"]}, {"file": "apps/integrations-api/src/google/google.controller.ts", "hash": "b884fa5343c7339d4512c46b202d64981584069d", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger"]}, {"file": "apps/integrations-api/src/google/google.module.ts", "hash": "7e9dd086c310a4c1c7c986c62390ed3e6f0c3165", "deps": ["npm:@nestjs/common"]}, {"file": "apps/integrations-api/src/google/google.service.spec.ts", "hash": "95bda3e8270f910e9c683ceab0e841a56931cbc8", "deps": ["npm:@nestjs/testing"]}, {"file": "apps/integrations-api/src/google/google.service.ts", "hash": "2012f876ade7e50a15902751909bfacac4c374d5", "deps": ["npm:@nestjs/common", "npm:@google-cloud/talent"]}, {"file": "apps/integrations-api/src/healthcheck/healthcheck.controller.ts", "hash": "b5dd37c2f866098d4e22781af238412f0b54e0ce", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger"]}, {"file": "apps/integrations-api/src/healthcheck/healthcheck.module.ts", "hash": "36dbcf8a2ffa556d2a45f074d1fc74eddf84035c", "deps": ["npm:@nestjs/common"]}, {"file": "apps/integrations-api/src/healthcheck/healthcheck.service.ts", "hash": "9c1173622c88fafb9f8753648d5468547017ff35", "deps": ["npm:@nestjs/common"]}, {"file": "apps/integrations-api/src/main.ts", "hash": "58d0ee774853066f48830887e16c9aade9bf7e51", "deps": ["npm:@nestjs/common", "npm:@nestjs/core", "npm:@nestjs/swagger"]}, {"file": "apps/integrations-api/tsconfig.app.json", "hash": "515cb2d76f75cf59bd88a173bd9e3a336d6a6846"}, {"file": "apps/integrations-api/tsconfig.json", "hash": "63dbe35fb282d5f9ac4a724607173e6316269e29"}, {"file": "apps/integrations-api/tsconfig.spec.json", "hash": "a18afb604688956c6a84dd7780d99923f0c04662"}]}}, "assessment-api": {"name": "assessment-api", "type": "app", "data": {"root": "apps/assessment-api", "sourceRoot": "apps/assessment-api/src", "projectType": "application", "targets": {"build": {"executor": "@nrwl/node:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/assessment-api", "main": "apps/assessment-api/src/main.ts", "tsConfig": "apps/assessment-api/tsconfig.app.json", "assets": ["apps/assessment-api/src/assets"], "generatePackageJson": true}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/assessment-api/src/environments/environment.ts", "with": "apps/assessment-api/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nrwl/node:execute", "options": {"buildTarget": "assessment-api:build"}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/assessment-api/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/apps/assessment-api"], "options": {"jestConfig": "apps/assessment-api/jest.config.js", "passWithNoTests": true}}}, "tags": [], "files": [{"file": "apps/assessment-api/.eslintrc.json", "hash": "9d9c0db55bb1e91c5f2e7b64a02bc6bf69fc7cb5"}, {"file": "apps/assessment-api/jest.config.js", "hash": "c6178a82f9157a25bb3603644d8a55d6fa2692dc"}, {"file": "apps/assessment-api/project.json", "hash": "e2fbd91dea5d10ecae746d95039e6eabac08d8c0"}, {"file": "apps/assessment-api/src/app/.gitkeep", "hash": "e69de29bb2d1d6434b8b29ae775ad8c2e48c5391"}, {"file": "apps/assessment-api/src/app/app.controller.spec.ts", "hash": "0c13b01cc69f62c136fd93cec0815674f3802f4e", "deps": ["npm:@nestjs/testing"]}, {"file": "apps/assessment-api/src/app/app.controller.ts", "hash": "8003c18a478c307339543bff84248e4bf36f7ace", "deps": ["code-executor", "db", "npm:@nestjs/swagger", "npm:@nestjs/common", "auth", "npm:@nestjs/passport"]}, {"file": "apps/assessment-api/src/app/app.module.ts", "hash": "65c6748185c3d2b85c2cd8006a4731f1d9ed30ee", "deps": ["auth", "code-executor", "db", "editor-manager", "email", "npm:@nestjs/common", "npm:@nestjs/microservices", "npm:@nestjs/axios", "temporal"]}, {"file": "apps/assessment-api/src/app/app.service.spec.ts", "hash": "f4f43b5ccd5e08a0b29068328317de5ef50526f9", "deps": ["npm:@nestjs/testing"]}, {"file": "apps/assessment-api/src/app/app.service.ts", "hash": "e4f0333bf74a817733a9ff653a74ffa3f7f5cccc", "deps": ["npm:@nestjs/common"]}, {"file": "apps/assessment-api/src/app/assessment-chatbot/assessment-chatbot.controller.ts", "hash": "ddbe3aa5d85157fc29391d102c226e0841bbc9cd", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "db", "npm:@nestjs/passport", "auth"]}, {"file": "apps/assessment-api/src/app/assessment-database/assessment-database.controller.ts", "hash": "1a669a4528cad5809afda03328355d8c6d73d9aa", "deps": ["npm:@nestjs/common", "db", "npm:@nestjs/swagger", "auth", "npm:@nestjs/passport"]}, {"file": "apps/assessment-api/src/app/assignment/assignment.controller.ts", "hash": "fbc8c8101ffac30a16de71197ab099adae48aeea", "deps": ["db", "email", "npm:@nestjs/passport", "npm:@nestjs/common", "npm:@nestjs/swagger", "auth"]}, {"file": "apps/assessment-api/src/app/calendar/calendar.controller.ts", "hash": "9cc0c9cf0e88af9961c851f0b96de782c66161a7", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "npm:@nestjs/passport", "auth", "db"]}, {"file": "apps/assessment-api/src/app/coding-area/coding-area.gateway.spec.ts", "hash": "422f8ed5bbefa5a1a899640b6d68804733af18fe", "deps": ["npm:@nestjs/testing"]}, {"file": "apps/assessment-api/src/app/coding-area/coding-area.gateway.ts", "hash": "c19a3e5f5e7dfd43777263a4fb76594a0e4da816", "deps": ["db", "npm:@nestjs/common", "npm:@nestjs/websockets", "editor-manager"]}, {"file": "apps/assessment-api/src/app/domain-questions/domain-questions.controller.ts", "hash": "a9a8e9d1ad6701ac6ebcac3d82702a8aa01c073a", "deps": ["db", "npm:@nestjs/common", "npm:@nestjs/swagger", "auth", "npm:@nestjs/passport", "npm:@nestjs/axios", "temporal"]}, {"file": "apps/assessment-api/src/app/domain-questions/pagination.dto.ts", "hash": "7bf2fee5f0a80c9a5c06cb9cc39b1b2a55877350", "deps": ["npm:class-validator"]}, {"file": "apps/assessment-api/src/app/domain-result/domain-result.controller.ts", "hash": "e2e26ef33795cf9fe5ebd6e2288f2c40ece0974c", "deps": ["auth", "npm:@nestjs/common", "npm:@nestjs/passport", "npm:@nestjs/swagger", "db", "temporal", "npm:@nestjs/axios"]}, {"file": "apps/assessment-api/src/app/domain-result/dto/create-domain-result.dto.ts", "hash": "0ce52a5b62d55eef7d33a32a35fa130194bf4bad", "deps": ["npm:class-validator"]}, {"file": "apps/assessment-api/src/app/domain-result/dto/get-domain-result.dto.ts", "hash": "bd0ff5d5e77d4fcbcdc96711d37ad4854114995a", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "apps/assessment-api/src/app/live-coding/live-coding.controller.ts", "hash": "f970b58a77058e3fb47bf78c7acefc18439a1a27", "deps": ["db", "npm:@nestjs/common", "npm:@nestjs/passport", "npm:@nestjs/swagger", "auth"]}, {"file": "apps/assessment-api/src/app/take-home/take-home-submission.controller.spec.ts", "hash": "7f52ce057c24e7787a748858e298d98a3ff99ab6", "deps": ["npm:@nestjs/testing", "npm:@nestjs/common"]}, {"file": "apps/assessment-api/src/app/take-home/take-home-submission.controller.ts", "hash": "486d676fd4132a5a22184cfe86bcdb8f664c777a", "deps": ["npm:@nestjs/common", "npm:@nestjs/passport", "npm:@nestjs/swagger", "auth", "db", "code-executor"]}, {"file": "apps/assessment-api/src/app/take-home/take-home-task.controller.ts", "hash": "0dfb44bea96fca13fbb45cbb1c1ea163d22d087d", "deps": ["auth", "db", "npm:@nestjs/common", "npm:@nestjs/passport", "npm:@nestjs/swagger"]}, {"file": "apps/assessment-api/src/app/test-manager/test-manager.controller.ts", "hash": "92a0f85dd41d75eca5edd8cc0688123a1ec31872", "deps": ["auth", "db", "npm:@nestjs/common", "npm:@nestjs/passport", "npm:@nestjs/swagger", "npm:@nestjs/axios", "temporal"]}, {"file": "apps/assessment-api/src/app/testcases/dto/generate-testcase.dto.ts", "hash": "c26b1787eb1387f1c617aa298b1784d6223bb8de", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "apps/assessment-api/src/app/testcases/testcase-generator.service.ts", "hash": "7823e7fd1035cc5ab6a4de6470da3901cd79fea9", "deps": ["npm:@nestjs/common", "npm:@nestjs/axios", "npm:@nestjs/config", "npm:rxjs"]}, {"file": "apps/assessment-api/src/app/testcases/testcase.controller.ts", "hash": "bedc6961241f04d508b398eaf2f28134ed4f799a", "deps": ["db", "npm:@nestjs/common", "npm:@nestjs/swagger"]}, {"file": "apps/assessment-api/src/app/testcases/testcase.module.ts", "hash": "16d4a3641be79b0bf3d259bb45b27731cf95f629", "deps": ["npm:@nestjs/common", "npm:@nestjs/axios", "npm:@nestjs/config", "db"]}, {"file": "apps/assessment-api/src/assets/.gitkeep", "hash": "e69de29bb2d1d6434b8b29ae775ad8c2e48c5391"}, {"file": "apps/assessment-api/src/assets/mysql.jar", "hash": "683ac268138ae43b8df6fd76f37eafed7236f05c"}, {"file": "apps/assessment-api/src/assets/postgresql.jar", "hash": "78cd5931fb2551939a7cea824fb65df95129f4b4"}, {"file": "apps/assessment-api/src/assets/sqlite-jdbc-3.27.2.1.jar", "hash": "51f759c6b1bb57d95baccc69fc74690699f36cd1"}, {"file": "apps/assessment-api/src/assets/templates/assessment.html", "hash": "cf518b4ca14276e7f3f61308c6a5dccb328b7694"}, {"file": "apps/assessment-api/src/assets/templates/jobDetail.html", "hash": "ab01932e5928848214ebee4c7e7723623f815ff3"}, {"file": "apps/assessment-api/src/assets/templates/schedule.html", "hash": "8b01b9fa2c2acdc6502fead6e280b80fd32a3918"}, {"file": "apps/assessment-api/src/assets/templates/trialEnds.html", "hash": "10a98f22d8ef863b6f78d16e2960b600b2b18ffc"}, {"file": "apps/assessment-api/src/main.ts", "hash": "5ab263351e5ba2f47e3722d1759e7d8bd21e377c", "deps": ["npm:@nestjs/common", "npm:@nestjs/core", "npm:@nestjs/microservices", "npm:@nestjs/swagger", "qdrant", "kinesis"]}, {"file": "apps/assessment-api/tsconfig.app.json", "hash": "515cb2d76f75cf59bd88a173bd9e3a336d6a6846"}, {"file": "apps/assessment-api/tsconfig.json", "hash": "63dbe35fb282d5f9ac4a724607173e6316269e29"}, {"file": "apps/assessment-api/tsconfig.spec.json", "hash": "a18afb604688956c6a84dd7780d99923f0c04662"}]}}, "editor-manager": {"name": "editor-manager", "type": "lib", "data": {"root": "libs/editor-manager", "sourceRoot": "libs/editor-manager/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/editor-manager/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/editor-manager"], "options": {"jestConfig": "libs/editor-manager/jest.config.js", "passWithNoTests": true}}}, "tags": [], "files": [{"file": "libs/editor-manager/.babelrc", "hash": "cf7ddd99c615a064ac18eb3109eee4f394ab1faf"}, {"file": "libs/editor-manager/.eslintrc.json", "hash": "9d9c0db55bb1e91c5f2e7b64a02bc6bf69fc7cb5"}, {"file": "libs/editor-manager/jest.config.js", "hash": "0009431ebc524a08032a762cfc614e4ba42bc4d9"}, {"file": "libs/editor-manager/project.json", "hash": "ca4a2edc3361cb3d174e6055035af43969a5a9d3"}, {"file": "libs/editor-manager/README.md", "hash": "b04c623da3b6aabf7d50ef01f4799e9db5a2337d"}, {"file": "libs/editor-manager/src/index.ts", "hash": "4965fc099383c4983b4b02655a0fc025f9db3ace"}, {"file": "libs/editor-manager/src/lib/editor-manager.module.ts", "hash": "bf06d4bd52aaac8c0fcc5622a17d9ff1ef5e413d", "deps": ["npm:@nestjs/common"]}, {"file": "libs/editor-manager/src/lib/editor-manager.service.spec.ts", "hash": "8f9a67a2a0393851fbb63b443ab8e86121776e6e", "deps": ["npm:@nestjs/testing"]}, {"file": "libs/editor-manager/src/lib/editor-manager.service.ts", "hash": "4faadc0bd5574c9c0a0224fdd37ff4c87514a902", "deps": ["npm:@nestjs/common"]}, {"file": "libs/editor-manager/src/lib/roomUser.interface.ts", "hash": "e69cd3099bbdcd8a9cecd1b2a33ff46466330d08"}, {"file": "libs/editor-manager/src/lib/roomUser.service.ts", "hash": "e1b9a5f5a741120c90f33fdb60380940b0250282", "deps": ["npm:@nestjs/common"]}, {"file": "libs/editor-manager/src/lib/settings.interface.ts", "hash": "6a3a51f1d0441b191c7593b877a028e36fc7e810"}, {"file": "libs/editor-manager/tsconfig.json", "hash": "62ebbd946474cea997e774d20fffc4d585c184f3"}, {"file": "libs/editor-manager/tsconfig.lib.json", "hash": "030dc8c8374ad9564038694332f9d864c54b05c4"}, {"file": "libs/editor-manager/tsconfig.spec.json", "hash": "67f149c4c04b9fad28e3b59462e55ba2d1e1d09b"}]}}, "recruitment-db": {"name": "recruitment-db", "type": "lib", "data": {"root": "libs/recruitment-db", "sourceRoot": "libs/recruitment-db/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/recruitment-db/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/recruitment-db"], "options": {"jestConfig": "libs/recruitment-db/jest.config.js", "passWithNoTests": true}}}, "tags": [], "files": [{"file": "libs/recruitment-db/.babelrc", "hash": "cf7ddd99c615a064ac18eb3109eee4f394ab1faf"}, {"file": "libs/recruitment-db/.eslintrc.json", "hash": "9d9c0db55bb1e91c5f2e7b64a02bc6bf69fc7cb5"}, {"file": "libs/recruitment-db/jest.config.js", "hash": "d5380b3f6761e91530432ca8ab96aab141c02c11"}, {"file": "libs/recruitment-db/project.json", "hash": "8e32713879d3c93c4aaddcc0d5854ef76d690431"}, {"file": "libs/recruitment-db/README.md", "hash": "933f773b47312fc13024d6b780d703005928a484"}, {"file": "libs/recruitment-db/src/index.ts", "hash": "8f03d63368b97346d1c73ba83591b4569bf7fae5"}, {"file": "libs/recruitment-db/src/lib/addresses/addresses.model.ts", "hash": "ee589c430b0bf2a0125985e5e2bbecb5a9ec0bd4", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/addresses/addresses.module.ts", "hash": "4ca26d336281272f63982e4ea55eefc3df41443e", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/addresses/addresses.service.ts", "hash": "601efc8e6ba25d3b15fede154d9618d654ae7bd1", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/addresses/dto/address.dto.ts", "hash": "5b5988583141b956965f9dbb09b083c3b732473f", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/addresses/dto/create-address.dto.ts", "hash": "7eeee0c2f043680597de680ea69a4337e7360f82", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/addresses/dto/update-address.dto.ts", "hash": "d179334e4d3060b597d57ac1815f934e6b1b7991", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/awards/awards.model.ts", "hash": "8fd7acd79e39523b3314eb013042bf02baf7260a", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/awards/awards.module.ts", "hash": "78506818e8e16dee340b81208d70696bdd734a5f", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/awards/awards.service.ts", "hash": "ed05fc215ce4ce2745c97876f826bdac25cab42e", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/awards/dto/award.dto.ts", "hash": "23c5e52bd41075c9a5811bb1b06ff7f7179ebb5b", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/awards/dto/update-award.dto.ts", "hash": "223c3569ecd2ef6bbe47d711e56aaf3dce7c2a5c", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/background-screening/background.dto.ts", "hash": "7521d0da53ffb6f0fd7a5a9fd07636feceabe8c1", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/background-screening/background.enum.ts", "hash": "5d37a0cbc6e2b358f3ec0aef5c648c3c90bbd46e"}, {"file": "libs/recruitment-db/src/lib/background-screening/background.model.ts", "hash": "56dcc0e762d25e0b316fe97d9892afca9ebeb11b", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/background-screening/background.module.ts", "hash": "1ee189e4e125b88695a5977b9db5db75bd9240e0", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "pre-recruitment-api", "npm:@nestjs/axios", "email"]}, {"file": "libs/recruitment-db/src/lib/background-screening/background.service.ts", "hash": "fe218528f91964fe2009dd6b129eac7c264e1b58", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize", "pre-recruitment-api", "npm:@nestjs/axios", "temporal", "email"]}, {"file": "libs/recruitment-db/src/lib/candidateoffers/candidate-offers.module.ts", "hash": "f36bf3f1bb54f05133f1c90bd7c13070dae2b715", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/candidateoffers/candidate-offers.service.ts", "hash": "c9472ef1b9c091350a407098d24b80be9908d9c4", "deps": ["npm:@nestjs/common", "npm:sequelize", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/candidateoffers/dto/candidate-offers.dto.ts", "hash": "3e6fa68f1e7e0faba42b7652b17a76c00ddac135", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/candidates/candidates.model.ts", "hash": "782d8b3e4382c6a85a701b61af1e315740ef47fc", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/candidates/candidates.module.ts", "hash": "e51b0900d4f7faaadce286305f5c97cd8600005e", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "auth", "pre-recruitment-api", "integrations", "email"]}, {"file": "libs/recruitment-db/src/lib/candidates/candidates.service.ts", "hash": "c4468ffcb28e36cb5483536f7f9f48bd319338b4", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "auth", "npm:sequelize", "pre-recruitment-api", "integrations", "npm:uuid", "email", "npm:bcrypt"]}, {"file": "libs/recruitment-db/src/lib/candidates/dto/candidate-filters.dto.ts", "hash": "8c7c5e8a2c71b886398d9c7ee3e04082155e5fc9", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/candidates/dto/candidate.dto.ts", "hash": "447957f3d80493abaebdf0aef7e45d4df82247a1", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/candidates/dto/invite-candidate.dto.ts", "hash": "e0bd006e83bef0915ee95c90a80948d7294c8eef", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/candidates/dto/update-candidate.dto.ts", "hash": "9d2e188af45291f2d4f1cd800f40a34cde1ff893", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/certificates/certificates.model.ts", "hash": "16a77db415b2a62dbb5a06c8d3dcf1eaf06c0d70", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/certificates/certificates.module.ts", "hash": "ce05d3bc7aed1b5e2245c9d4ea32aadd5d8f3c8b", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/certificates/certificates.service.ts", "hash": "5a38ff8a74e3c4208431994de63ca41d73bfcde5", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/certificates/dto/certificate.dto.ts", "hash": "ac68d408eecd97e971e9a97c0caabc145c0e5e05", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/certificates/dto/update-certificate.dto.ts", "hash": "9499edf6e17f911201efde4ee93028df79c96c63", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/companies/companies.model.ts", "hash": "023ed0a58364cfdec96461d4d8ea2393e7f4efca", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/companies/companies.module.ts", "hash": "77a1b09d9b0ad41001b5eb8aec6ffda5e0239403", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "auth", "email", "npm:@nestjs/axios"]}, {"file": "libs/recruitment-db/src/lib/companies/companies.service.ts", "hash": "d3913b6f1fe879283252fbdb8631c5fefdf9f084", "deps": ["npm:@nestjs/common", "email", "npm:@nestjs/sequelize", "auth", "npm:sequelize", "npm:@nestjs/axios"]}, {"file": "libs/recruitment-db/src/lib/companies/dto/company-roles.dto.ts", "hash": "d0038489098e3c635cb8e68ad970abf1cdccf096", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/companies/dto/company.dto.ts", "hash": "c1c889fd6c7e9f9f24560f5a40b64acedd97b945", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/companies/dto/editPermissionsForRole.dto.ts", "hash": "889ca411c3f31ca86bc7725be9c4965a4fec271d", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/companies/dto/getCompaniesForJob.dto.ts", "hash": "5ec277e1e433d141556250c20aca5a841a13adc2", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/companies/dto/getPublicCompanies.dto.ts", "hash": "8d543e99a6620519912ea7ef509e86954c777817", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/companies/dto/IAMUsers.dto.ts", "hash": "4fef91f0e6fd4f0f6346bdde8e8b0a1965399671", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/companies/dto/register-company.dto.ts", "hash": "723cbf9a64471f471977dd1679ee254ec1ae2ab8", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/companies/dto/send-company-email.dto.ts", "hash": "a1c2be70fd184f1c19b6926a2f60e5252c5d0e11", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/companies/dto/update-company.dto.ts", "hash": "743d7adb97ea78f8bad5e3a2e1ce1c47ac3f9f95", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/department/data.json", "hash": "2dd60bae2852530d99267842a08a0d3f20316e9f"}, {"file": "libs/recruitment-db/src/lib/department/department.model.ts", "hash": "6b9afe9b52a40a676eb061d1dd80a604b1fbd651", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/department/department.module.ts", "hash": "9f6ebd4536e9f91aa070e2df0726a6e5f3edbade", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/department/department.service.ts", "hash": "793023accf9a67d65ca63e550d39b28b6d436c3a", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize"]}, {"file": "libs/recruitment-db/src/lib/drug-screening/drug.dto.ts", "hash": "13ecf9785fa86328bf7225993b9101ef36e70f86", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/drug-screening/drug.enum.ts", "hash": "fdc0dbc20d31626347464bc44a486079ee8ff9e2"}, {"file": "libs/recruitment-db/src/lib/drug-screening/drug.model.ts", "hash": "651620ce692abe5156f31e8a22680cbb5f14f993", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/drug-screening/drug.module.ts", "hash": "8c24f891d36ea01e5d41146e725da99f612e58e1", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:@nestjs/axios", "pre-recruitment-api"]}, {"file": "libs/recruitment-db/src/lib/drug-screening/drug.service.ts", "hash": "2ecddd69762a82d301b78774919a4548087c2fcc", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize", "temporal", "npm:@nestjs/axios", "pre-recruitment-api"]}, {"file": "libs/recruitment-db/src/lib/educations/dto/create-education.dto.ts", "hash": "64ae085d9c9dc2af9e747544671b64fed9185dd4", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/educations/dto/education.dto.ts", "hash": "f6c253f38918062e289627873cd008404dda2442", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/educations/dto/update-education.dto.ts", "hash": "5e7bfe85419ad91bfe04a8b88cc0bced52826e43", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/educations/educations.model.ts", "hash": "a5318359770499d5aed3add938182e499d599692", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/educations/educations.module.ts", "hash": "139a9b07f5d6c4d88b712d9502cd65b2538fd119", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/educations/educations.service.ts", "hash": "679fb6b5cdeac46eeaa318610f55d106e2ea2f2b", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/experiences/dto/create-experience.dto.ts", "hash": "cbe7ea140792c4c2d20b0f1bbc7d71f75173bad7", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/experiences/dto/experience.dto.ts", "hash": "85d9c015870b46cf1ac34d9cb325c381d8215b20", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/experiences/dto/update-experience.dto.ts", "hash": "8154f289314e394430b38c3bd88fbd14bac6f240", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/experiences/experiences.model.ts", "hash": "4d3515abdc358e42c87632b2137a854570ee7c88", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/experiences/experiences.module.ts", "hash": "120893b12467ca2dbcf41c7706d3e83205917bec", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/experiences/experiences.service.ts", "hash": "527e1530db0d692b4a9613bcfce1f5f025cd2ce3", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/freshsales/freshsales.dto.ts", "hash": "709e3de19e33fa9ec11ece1a268819d62ff595d2", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/freshsales/freshsales.module.ts", "hash": "6e4b8149d7ff74a4700d91ce583e2a150a349486", "deps": ["npm:@nestjs/common", "npm:@nestjs/axios"]}, {"file": "libs/recruitment-db/src/lib/freshsales/freshsales.service.ts", "hash": "6eb4d1536eca7c45134e5fec6a865000201d0f23", "deps": ["npm:@nestjs/axios", "npm:@nestjs/common"]}, {"file": "libs/recruitment-db/src/lib/gmail-mailbox/dto/mailFilter.dto.ts", "hash": "ed734e8598ae483a850ec21c07388b1df31e770a", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/gmail-mailbox/dto/sendMail.dto.ts", "hash": "3c4b541a55669441404ac579b3de00dd41c0dee6", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/gmail-mailbox/mailbox.model.ts", "hash": "0e0a4f1acf380d4e4dfe5b06019e7d3cef883e55", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/gmail-mailbox/mailbox.module.ts", "hash": "6c5414f768dcb4a87b5fe73f70c54bff344ea7cb", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "pre-recruitment-api", "email"]}, {"file": "libs/recruitment-db/src/lib/gmail-mailbox/mailbox.service.ts", "hash": "29121f038337a20157b74a962c16747202ac08eb", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize", "email"]}, {"file": "libs/recruitment-db/src/lib/gmail/dto/mailFilter.dto.ts", "hash": "ed734e8598ae483a850ec21c07388b1df31e770a", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/gmail/dto/sendMail.dto.ts", "hash": "3c4b541a55669441404ac579b3de00dd41c0dee6", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/gmail/gmail.module.ts", "hash": "d1204bd2ca3a18e2f41f7a587451b83f2a168dad", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "pre-recruitment-api"]}, {"file": "libs/recruitment-db/src/lib/gmail/gmail.service.ts", "hash": "ed8e4e05ded72fd994fb4348380076760b4b1d98", "deps": ["npm:@nestjs/common", "npm:googlea<PERSON>", "pre-recruitment-api"]}, {"file": "libs/recruitment-db/src/lib/hellosign/dto/hellosign-integration.dto.ts", "hash": "fb21b198fd533a3b5ebb027ad91ca18fe4c564ec", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/hellosign/hellosign-integration.model.ts", "hash": "877bf12b1524cd422accbad56bd49dd55eb435a8", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/hellosign/hellosign-integration.module.ts", "hash": "5598eba35a752576b1fbfbebd8d4fa83e7d14a7f", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "pre-recruitment-api"]}, {"file": "libs/recruitment-db/src/lib/hellosign/hellosign-integration.service.ts", "hash": "5746a6404d2f7ec1de3272229bad0e3a79c6aaa0", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize"]}, {"file": "libs/recruitment-db/src/lib/hooks/hooks.ts", "hash": "dff4dce37983569fc9a54026aba7262c7d0cdc3e"}, {"file": "libs/recruitment-db/src/lib/industries/candidates-industries.model.ts", "hash": "70d1982206481c704e1801efe54f19c456c8e80e", "deps": ["npm:sequelize-typescript"]}, {"file": "libs/recruitment-db/src/lib/industries/data.json", "hash": "3588ab46be3c0e6e54219805ac01539eea5a2100"}, {"file": "libs/recruitment-db/src/lib/industries/dto/industry.dto.ts", "hash": "e0a8b2914655047a0879d92e92adb8a245106405", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/industries/industries.model.ts", "hash": "f474c0dfcb0c2e0ab0078729d97427ead33ef87f", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/industries/industries.module.ts", "hash": "43945a026b6a41f9811889726aedbd0ff6ec76ef", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/industries/industries.service.ts", "hash": "e15c8aca0821b34bb628f3c756e421562b2d6956", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize"]}, {"file": "libs/recruitment-db/src/lib/integrationAuth/integrationAuth.model.ts", "hash": "363935c43313eb86822d05e12840d9c485bfd787", "deps": ["npm:@nestjs/swagger", "npm:sequelize-typescript"]}, {"file": "libs/recruitment-db/src/lib/integrationAuth/integrationAuth.module.ts", "hash": "4cd47978cb2d5d30d66a53e867c169c19d14d4ba", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/integrationAuth/integrationAuth.service.ts", "hash": "c615effe209e152768b819f4de0af57e69cbee0d", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/integrations/gcal/auth.service.ts", "hash": "2a2b55f0d828b8cae2e7640cf412964ab6121a41", "deps": ["npm:googlea<PERSON>", "npm:@nestjs/common"]}, {"file": "libs/recruitment-db/src/lib/integrations/gmail/auth.service.ts", "hash": "aec3f31b57b85046a46768945e7cd2b07ccb89af", "deps": ["npm:googlea<PERSON>", "npm:@nestjs/common"]}, {"file": "libs/recruitment-db/src/lib/integrations/hellosign/hellosign.service.ts", "hash": "5e6408b0c79d10e7264df86d09d23c5e1b41c2ab", "deps": ["npm:@nestjs/common", "npm:@dropbox/sign"]}, {"file": "libs/recruitment-db/src/lib/integrations/integrations.model.ts", "hash": "4331a956de270602be944b43fd1ac7482c0878f1", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/integrations/integrations.module.ts", "hash": "e7fda56abe1d72d491b44db29fb42d8f75ff3bcc", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:@nestjs/platform-express", "npm:@nestjs/axios"]}, {"file": "libs/recruitment-db/src/lib/integrations/integrations.service.ts", "hash": "79c17852f5a3397d7ec161d1de72d5f731fca061", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/integrations/mcal/auth.service.ts", "hash": "ab41af4787fa81a5f72afd82ca7265ec97b7d4d9", "deps": ["npm:@microsoft/microsoft-graph-client", "npm:@nestjs/common", "npm:cross-fetch", "npm:isomorphic-fetch"]}, {"file": "libs/recruitment-db/src/lib/integrations/outlook/auth.service.ts", "hash": "c1c2879a347b6b5c4adcd73a9d2324f8457ad5b3", "deps": ["npm:@microsoft/microsoft-graph-client", "npm:@nestjs/common", "npm:cross-fetch", "npm:isomorphic-fetch"]}, {"file": "libs/recruitment-db/src/lib/integrations/universal/universal.service.ts", "hash": "487f9f42dcae8445745d96147c33ab45147ea13d", "deps": ["npm:@nestjs/axios", "npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize"]}, {"file": "libs/recruitment-db/src/lib/interviews/interviews.dto.ts", "hash": "6a89a75b4238e42885d447025d1b2cc612b976c8", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/interviews/interviews.module.ts", "hash": "dc0a537223cbe144dc8d79b525f6a722d7a2ca1d", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/interviews/interviews.service.ts", "hash": "7533be03236abf48c6f729b98cc93e907812e147", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize"]}, {"file": "libs/recruitment-db/src/lib/job/chatbot/job-creation-chatbot/dto/job-creation-chat.dto.ts", "hash": "8bf1c84abff98328ff2847cf47663d8f651ac87b", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/job/chatbot/job-creation-chatbot/job-creation-chatbot.module.ts", "hash": "7a38fc41a80a0286260ab2bd34bbb46c59029b11", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "integrations", "npm:@nestjs/axios"]}, {"file": "libs/recruitment-db/src/lib/job/chatbot/job-creation-chatbot/job-creation-chatbot.service.ts", "hash": "c34ea2b0a309ba7d53554914f9b24edf57f6f55a", "deps": ["npm:@nestjs/common", "npm:zod", "npm:@langchain/openai", "npm:langchain", "npm:@nestjs/sequelize", "integrations", "npm:@qdrant/qdrant-js", "npm:uuid", "npm:@nestjs/axios"]}, {"file": "libs/recruitment-db/src/lib/job/dto/filter-job.dto.ts", "hash": "bb3a9d0a6b06475d0e3aa565cdd64f21a33751f0", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/job/dto/job.dto.ts", "hash": "b213bb5888c7f21d7250c4b0f8a36474b2b18f13", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/job/dto/update-job.dto.ts", "hash": "6791947ff68335a8fb672e5e5144a57e01340a10", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/job/job.model.ts", "hash": "9ad82e6cab56b10181f1e721913c8aff6c275953", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger", "npm:aws-sdk"]}, {"file": "libs/recruitment-db/src/lib/job/job.module.ts", "hash": "4526599ebd1f82bce407c318a048ddbfc5178635", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "integrations", "email", "npm:@nestjs/axios"]}, {"file": "libs/recruitment-db/src/lib/job/job.service.ts", "hash": "3f8e5f427bd5141d01d947284af4f95f8833fe1b", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize", "integrations", "email", "npm:@nestjs/axios", "temporal", "qdrant"]}, {"file": "libs/recruitment-db/src/lib/jobtarget/jobtarget.dto.ts", "hash": "83ffed64da45fe26840003746fe4985ea80fb13a", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/jobtarget/jobtarget.model.ts", "hash": "39afe764f995e5996cead2ec6bdb32ebf3ddb5e5", "deps": ["npm:sequelize-typescript"]}, {"file": "libs/recruitment-db/src/lib/jobtarget/jobtarget.module.ts", "hash": "5fd4c91024c81827ed0869117df22e023dff2a1a", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/jobtarget/jobtarget.service.ts", "hash": "0e9b5b58f3fc7315b3b1a36623f880733325bfad", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:zod"]}, {"file": "libs/recruitment-db/src/lib/locations/location-jobs.model.ts", "hash": "3fc0b67addc298480d45d8cf46db41591d46151b", "deps": ["npm:sequelize-typescript"]}, {"file": "libs/recruitment-db/src/lib/locations/location.model.ts", "hash": "7f14a3af7ff51795cdca5d28a4ad83122e8e6afd", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/locations/locations.module.ts", "hash": "0da5251a28cbadcf25cb076a0bcb5b4a48c79ae4", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/locations/locations.service.ts", "hash": "0812c6f420c5091ba4744a86b3251583a3129a0d", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize"]}, {"file": "libs/recruitment-db/src/lib/locations/zipdata.json", "hash": "8901219e9c49b3d340a729cd0efd31836961e531"}, {"file": "libs/recruitment-db/src/lib/notifications/dto/notifications.dto.ts", "hash": "6f357d48ca2107368145bd96b8c5513748a40529", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/notifications/notifications.model.ts", "hash": "ed2680d1bf7a19303dd42e58a427b5654d348fbd", "deps": ["npm:@nestjs/swagger", "npm:sequelize-typescript"]}, {"file": "libs/recruitment-db/src/lib/notifications/notifications.module.ts", "hash": "1582753e240bf95e40d4271334059b871f42ff3a", "deps": ["npm:@nestjs/sequelize", "npm:@nestjs/common"]}, {"file": "libs/recruitment-db/src/lib/notifications/notifications.service.ts", "hash": "8bd1b5b2239a4444590ef09f723cebd808a1bf3b", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/notify-permissions/dto/notify-permissions.dto.ts", "hash": "b3506eb5f49d59b91db23554ae3ddf8d9c5bcaea", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/notify-permissions/notify-permissions.model.ts", "hash": "bf5d3502b40b38cf5db9f11ca00bb6ef127d6e4e", "deps": ["npm:@nestjs/swagger", "npm:sequelize-typescript"]}, {"file": "libs/recruitment-db/src/lib/notify-permissions/notify-permissions.module.ts", "hash": "dfa4e7332689d2e1225f6c1be24646452448825f", "deps": ["npm:@nestjs/sequelize", "npm:@nestjs/common"]}, {"file": "libs/recruitment-db/src/lib/notify-permissions/notify-permissions.service.ts", "hash": "f7394c6c259d1f3c68f31e7c29d1288e30eae34b", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:firebase-admin"]}, {"file": "libs/recruitment-db/src/lib/notify-permissions/seviceAccountKey.json", "hash": "35f18d7a15527f4673249db23329dda526775d0b"}, {"file": "libs/recruitment-db/src/lib/offers/dto/offer.dto.ts", "hash": "9bb06d584b673641e3fb51579b34f69d62a450af", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/offers/dto/template.dto.ts", "hash": "be63d4fffc4d62b793c5769e0ac4ed468341cc29", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/offers/hellosign-template.model.ts", "hash": "12d09ed8618c1481748411576bce1ba37d603855", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/offers/mailData/candidate.mail.data.ts", "hash": "2d4338b17e0bfca1262a2fe29c8da06358189d00"}, {"file": "libs/recruitment-db/src/lib/offers/mailData/recuiter.mail.data.ts", "hash": "411a64a961e5564f103a4932ac00c1a3968550ec"}, {"file": "libs/recruitment-db/src/lib/offers/offer.enum.ts", "hash": "48cb06b1e4aedad237804acdc5a367ad7791c9cd"}, {"file": "libs/recruitment-db/src/lib/offers/offer.model.ts", "hash": "31724791e096b0dcda3651e228fe1a55682b7f2f", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/offers/offer.module.ts", "hash": "4dd4c05f48b0dde3a1c9bdfbfb6e85340bc2ed08", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "pre-recruitment-api", "email", "temporal"]}, {"file": "libs/recruitment-db/src/lib/offers/offer.service.ts", "hash": "02bea899028ef4b7b148819807a5a690ba10ad53", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize", "pre-recruitment-api", "npm:puppeteer", "email", "temporal"]}, {"file": "libs/recruitment-db/src/lib/outlook/dto/mailFilter.dto.ts", "hash": "cda2b857ad6b41f6824cbfc92af5d2776dbbc363", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/outlook/dto/sendMail.dto.ts", "hash": "495ea915a8988dff68372790978a4c19064deedb", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/outlook/outlook.module.ts", "hash": "d8db881c7b7be0c992c4dea9a06240df7eb8b63a", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "pre-recruitment-api"]}, {"file": "libs/recruitment-db/src/lib/outlook/outlook.service.ts", "hash": "c5b80b75d09ee3e0bfdedde14344163011234a8d", "deps": ["npm:@nestjs/common", "pre-recruitment-api", "npm:@microsoft/microsoft-graph-client", "npm:cross-fetch", "npm:isomorphic-fetch"]}, {"file": "libs/recruitment-db/src/lib/payment/dto/paymentMethod.dto.ts", "hash": "3fca622cd01cde4ad6b4e439fff07558c6a31652", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/payment/dto/paymentMethodId.dto.ts", "hash": "d291d945813844577f265ae7674f6e5a44998496", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/payment/dto/subscriptionId.dto.ts", "hash": "3a456a5ddf884b3706a035f8b0ee5b99199c747b", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/payment/payment.model.ts", "hash": "02505188aa3707caf687554340fc0746a5036769", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/payment/payment.module.ts", "hash": "83a3ae874604d081aa7955e222f9fd0047fd4189", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "email"]}, {"file": "libs/recruitment-db/src/lib/payment/payment.service.ts", "hash": "f84bbc63e079e55bf00ec60a62d70784846e5a39", "deps": ["npm:@nestjs/common", "npm:nestjs-stripe", "npm:stripe", "npm:@nestjs/sequelize", "email", "npm:sequelize"]}, {"file": "libs/recruitment-db/src/lib/plan-packages/plan-packages.model.ts", "hash": "8dadcb42def12d7784f186528d6f9691b528fdc8", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/plan-packages/plan-packages.module.ts", "hash": "f2a9452db193007d2f4696034d88d5b0d650e2d4", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/plan-packages/plan-packages.service.ts", "hash": "da6b2f570bb369d471261c9b7204c9b747f494bb", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize"]}, {"file": "libs/recruitment-db/src/lib/plan-packages/planpackagesdata.json", "hash": "5a01169befae4e0c7fefb2161b75e11f2314eda3"}, {"file": "libs/recruitment-db/src/lib/plan/plan.model.ts", "hash": "3978ad27f3b0972980ae5f1699ce5457bf185a9b", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/plan/plan.module.ts", "hash": "632a65bef239e882d8a17a65e320ab9ea4323cb5", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/plan/plan.service.ts", "hash": "b4f90547048a4c5628217df5dcd239cc05c8d587", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize"]}, {"file": "libs/recruitment-db/src/lib/plan/plandata.json", "hash": "5498a9aa2533c574ed1a6c71ab3d198cd57f57e9"}, {"file": "libs/recruitment-db/src/lib/playback/dto/create-playback.dto.ts", "hash": "e47d94b607ec58bdfe930479093b7179427ed59a", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/playback/dto/searchPlayback.dto.ts", "hash": "d2d5c87e4a67daa8a499af1b25191f5976cfa337", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/playback/playback.model.ts", "hash": "67f9f744c2d6ca88d581f610df969fa2d4849027", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/playback/playback.module.ts", "hash": "d72c739fd84b1d35716c046fb195cdc0e6b6a859", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/playback/playback.service.ts", "hash": "7394a47373bcec7272402f9974847060ac186535", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize", "temporal"]}, {"file": "libs/recruitment-db/src/lib/positions/data.json", "hash": "4c4128b6416c9b7e7c20701ce1ef45f19748ce66"}, {"file": "libs/recruitment-db/src/lib/positions/new_data.json", "hash": "ae077d8fdaf8307f20cfbecce4c307a6e26b3d54"}, {"file": "libs/recruitment-db/src/lib/positions/positions.model.ts", "hash": "3cbad375e5f4b24fb3a9b7661353c87f833ed697", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/positions/positions.module.ts", "hash": "8aef0924fff1c09274d768795b264bc19f7ac469", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/positions/positions.service.ts", "hash": "ab13db64ab4316581886811b5a1faff1b0016f4c", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize"]}, {"file": "libs/recruitment-db/src/lib/predefined-skills/data.json", "hash": "957095c213f264ec295b9b077c0afba1b028e1c4"}, {"file": "libs/recruitment-db/src/lib/predefined-skills/dto/predefined-skill.dto.ts", "hash": "635369726d4393ef1d919b54bb7d19113f962a97", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/predefined-skills/predefined-skill.model.ts", "hash": "67e9b244211822be0203422b196a988682057318", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/predefined-skills/predefined-skill.module.ts", "hash": "6b7ab2e715b21dd80504864c366d7dc5ccc6331d", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/predefined-skills/predefined-skill.service.ts", "hash": "dc43bdc0a927d60c1844e5989a1a42a451ab67f4", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize"]}, {"file": "libs/recruitment-db/src/lib/price/dto/create-price.dto.ts", "hash": "0581ac1df4896c7bc9d361ecec60c767e0eb0199", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/price/dto/update-price.dto.ts", "hash": "4e70e8b1190b9d18dbd57e191511f3f53a55e02c", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/price/price.model.ts", "hash": "9e777a9de582e4dbe2070f022a844c9f43f68c45", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/price/price.module.ts", "hash": "accd0575eec92c82588512ada186e66eb4ec2eb2", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/price/price.service.ts", "hash": "2ed4683cd5814837f72068f0a4ff8ffa2c1d44a9", "deps": ["npm:@nestjs/common", "npm:nestjs-stripe", "npm:stripe", "npm:@nestjs/sequelize", "npm:sequelize"]}, {"file": "libs/recruitment-db/src/lib/price/pricedata-dev.json", "hash": "34bc7144f4403779ddd3a5ad6568d781ac8ff226"}, {"file": "libs/recruitment-db/src/lib/price/pricedata.json", "hash": "ddd1faa768ad73f1d2003280e77aed84859f9ff4"}, {"file": "libs/recruitment-db/src/lib/product/dto/create-product.dto.ts", "hash": "3d2d0947e5ae260c71f9df6e4fe5b2ba40b9322f", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/product/dto/update-product.dto.ts", "hash": "a84af7d0ee61376e88d719bddb69cd078c85e2bd", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/product/product.model.ts", "hash": "a927b357b2c54071b64c726ee0afeee638c7bd3f", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/product/product.module.ts", "hash": "f23976b68b8986aaf28d722775c621587d9ce479", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/product/product.service.ts", "hash": "219faee076b2746fc56aaae19cc363e5514957b8", "deps": ["npm:@nestjs/common", "npm:nestjs-stripe", "npm:stripe", "npm:@nestjs/sequelize", "npm:sequelize"]}, {"file": "libs/recruitment-db/src/lib/product/productdata-dev.json", "hash": "93b14e625de0423c39105a2fb06796cc63b7d76b"}, {"file": "libs/recruitment-db/src/lib/product/productdata.json", "hash": "9609fce153dd1940c3da0f8f5a8f776dc8ce2afc"}, {"file": "libs/recruitment-db/src/lib/projects/dto/projects.dto.ts", "hash": "42005659945c1039014ba9f385088daa024fa1bd", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/projects/dto/update-projects.dto.ts", "hash": "8ba005201570f4d5c55accdfed18db472038e60c", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/projects/projects.model.ts", "hash": "500b7df3e0680f4103e29d758ca1b59533a88d0a", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/projects/projects.module.ts", "hash": "743eefe7b047d6a6aae330568e7b1120ecfade3c", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/projects/projects.service.ts", "hash": "d32ab2f9e21565e6053f3ea46c09065ea8648f0d", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/recruiter-positions/data.json", "hash": "a682059a0b8fd2df054b077e0947cd5c06212395"}, {"file": "libs/recruitment-db/src/lib/recruiter-positions/recruiter-positions.model.ts", "hash": "a863afa90538b9e0431744f2296044911292745d", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/recruiter-positions/recruiter-positions.module.ts", "hash": "c2b95d93175a6921d3173ae8fbd2fb736b2a4937", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/recruiter-positions/recruiter-positions.service.ts", "hash": "07ae386a1c70d2a6bb37efc86472523c298b8ba8", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize"]}, {"file": "libs/recruitment-db/src/lib/recruiters/dto/add-recruiter.dto.ts", "hash": "fc0d79e683f8816d66e1e7a1145ebee7a98f87fb", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/recruiters/dto/edit-recruiter.dto.ts", "hash": "23f5680b8fbf03b7ca849cd18b6c69f73fdcf133", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/recruiters/dto/getMembers.dto.ts", "hash": "76d2bed97ee97629d87ed5ca1a981cf59b6d975b", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/recruiters/dto/profile-recruiter.dto.ts", "hash": "962774ef7489c760d99c363483a54243d5b1cfb0", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/recruiters/dto/send-mail.dto.ts", "hash": "5dfd26c70145924630824e2efec4a1369cd240db", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/recruiters/dto/update-profile-recruiter.dto.ts", "hash": "b4896574ac8cf143e5f2f4f38881a5a62356da58", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/recruiters/dto/verification.dto.ts", "hash": "6a53fd47b10cf265cd14416c55a63b2c3dd9b011", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/recruiters/recruiters.model.ts", "hash": "f242376ec461e1bf3e9dfa590b4e94a314be659d", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/recruiters/recruiters.module.ts", "hash": "6d6421683565b3762c9ba52aa932ef527e7628ad", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "auth", "email"]}, {"file": "libs/recruitment-db/src/lib/recruiters/recruiters.service.ts", "hash": "b656437fc7edcd2f3b088998d1060a49609ed8a0", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "auth", "npm:sequelize", "npm:uuid", "email", "npm:bcrypt"]}, {"file": "libs/recruitment-db/src/lib/recruitment-db.module.ts", "hash": "9bd8c35eed481cdee8f1d95f2487dcdae217f870", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:@nestjs/config", "auth", "integrations", "npm:nestjs-stripe", "email", "pre-recruitment-api", "npm:@nestjs/axios"]}, {"file": "libs/recruitment-db/src/lib/roles/data.json", "hash": "3a39d5edaf34009a7eac691161ada4796e9f0689"}, {"file": "libs/recruitment-db/src/lib/roles/dto/create-company-role.dto.ts", "hash": "902c30a5a08124d90958c17dedbb65ee79bc3901", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/roles/dto/role.dto.ts", "hash": "5ceca165ad314d29790ad9546b075ba526961301", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/roles/roles-dev.json", "hash": "2e74f9a0c519a77bf1e9c1dab84d6f35434a7c72"}, {"file": "libs/recruitment-db/src/lib/roles/roles.model.ts", "hash": "4534211ee2ae7db92013297c446a2565dd16d862", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/roles/roles.module.ts", "hash": "b1cdfa1815d4c522f36b5cebb6149e952bd7abce", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "auth"]}, {"file": "libs/recruitment-db/src/lib/roles/roles.service.ts", "hash": "969476f7e0ed01cc5aa0c1372c6a1ffabcb4f07f", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize", "auth"]}, {"file": "libs/recruitment-db/src/lib/roles/user-roles.model.ts", "hash": "27cab65b53be6000f7a15d3f564bd490dfc17713", "deps": ["npm:sequelize-typescript"]}, {"file": "libs/recruitment-db/src/lib/rounds/dto/add-event.dto.ts", "hash": "d7c5c92103e8346d00149a4b2ec6a221456e1ffd", "deps": ["npm:@nestjs/swagger", "temporal", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/rounds/dto/change-status.dto.ts", "hash": "8df084dcfa32f7e38924deb9f69c838696c22615", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/rounds/dto/round.dto.ts", "hash": "cb679bdbc36802b9ec53e5fc0c5933aeb5107e16", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/rounds/dto/update-round.dto.ts", "hash": "fd79b98421cfc560a579326c75817c266e96aae5", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/rounds/enum/roundStatus.enum.ts", "hash": "f4ca1d3345e98309fd851f30f2df60142b0f5b2e"}, {"file": "libs/recruitment-db/src/lib/rounds/round.model.ts", "hash": "e0818bb0b4f4b1a57fa7f1a09e4155004614271f", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/rounds/round.module.ts", "hash": "f4de65e13b9be1518a70a9324e03e36e208cab06", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:@nestjs/axios"]}, {"file": "libs/recruitment-db/src/lib/rounds/round.service.ts", "hash": "76d850553951253b5462e0cabd2dabd08d069ea6", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "temporal", "npm:sequelize", "npm:@temporalio/common", "npm:@nestjs/axios"]}, {"file": "libs/recruitment-db/src/lib/skills/dto/skills.dto.ts", "hash": "6d8a35773b48c8c3d15c4f84af0fe455f9c0c9cf", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/skills/dto/update-skills.dto.ts", "hash": "d82aa316d02c1688ee950d323b47ab9d6dcf586b", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/skills/skills.model.ts", "hash": "5c548bb5fbfd02253bd34aa4a2060fd491c8faa4", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/skills/skills.module.ts", "hash": "905a747f2c9dc1686f1ddc006c38e4f21b1f45d8", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/skills/skills.service.ts", "hash": "8b0e026c4b906a1c3acb9f3d0179708165d0bf31", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/subscribe/dto/create-subscribe.dto.ts", "hash": "f96d960ca673fa99a7834957ec696c7433d7830f", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/subscribe/dto/edit-subscribe.dto.ts", "hash": "59d8d714406645b8b5523d68283c6e48ab0526ef", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/subscribe/dto/filter-subscribe.dto.ts", "hash": "81825fe389d69a97c5a83afa85aa646894794693", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/subscribe/dto/subscribe.dto.ts", "hash": "3943c9adb6cbfc4573f9f3af6f8cf36cff057d96", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/subscribe/subscribe.model.ts", "hash": "8b991f79c0a1aaff8b8648d64b0e14f6465ca820", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/subscribe/subscribe.module.ts", "hash": "3bbd4c26d2aec23cb2caddbe3db81fdef130c471", "deps": ["email", "npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:@nestjs/axios"]}, {"file": "libs/recruitment-db/src/lib/subscribe/subscribe.service.ts", "hash": "1092977ad109367805ad9f98faf9c4adb224c639", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize", "npm:uuid", "npm:@nestjs/axios", "temporal", "email"]}, {"file": "libs/recruitment-db/src/lib/subscription/dto/companySubscription.dto.ts", "hash": "574179cc4cb1fd7cdc335a0d5b37594038015c46", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/subscription/dto/createSubscription.dto.ts", "hash": "72709789c85c110f4a3f047338b8c7e148e512eb", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/subscription/dto/createSubscriptionWithoutCard.dto.ts", "hash": "51e687537d9f161eea05ca9db48ca8ed2101324b", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/subscription/dto/subscriptionId.dto.ts", "hash": "3a456a5ddf884b3706a035f8b0ee5b99199c747b", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/subscription/dto/upgradeSubscription.dto.ts", "hash": "d0690b246e2d97dcf112ef1329bfd481eb1d14ff", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/subscription/subscription-addon.model.ts", "hash": "331b7f185313e557001678a41d92e1f7f695605b", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/subscription/subscription.module.ts", "hash": "bda96e413e9ecdfebf270928f90373849a360ec5", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/subscription/subscription.service.ts", "hash": "d251ffdcc7ab835beed923408b71e5844c763871", "deps": ["npm:@nestjs/common", "npm:nestjs-stripe", "npm:stripe", "npm:@nestjs/sequelize", "npm:sequelize"]}, {"file": "libs/recruitment-db/src/lib/subscription/subscriptions.model.ts", "hash": "a032313b73c5f9e1a926a4e5bfacd9d7416661c2", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/temporalWorkflow/dto/create-workflow.dto.ts", "hash": "f32869a70f1904830b4e949d57b8da3c39afc02d", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/temporalWorkflow/temporal.model.ts", "hash": "51cd2de9d081f65a631177e5ac80bd88b2f94c49", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/temporalWorkflow/temporal.module.ts", "hash": "9f4342148f81ec1e634b4d1cc6270b67e7b1aef3", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/temporalWorkflow/temporal.service.ts", "hash": "c808107d0c884ea13a289c63ff8e59abce93b4d5", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/universal/universal.dto.ts", "hash": "0fcc6054731bb855133dac92a562c7e78fc4318d", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/universal/universal.enum.ts", "hash": "93c891e237394c5becce6b00e2e12bc392b9ea0e"}, {"file": "libs/recruitment-db/src/lib/universal/universal.model.ts", "hash": "c9fbb35458f6b0efcfa6b40346c55740037e0882", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/universal/universal.module.ts", "hash": "df46599753431faa5d547b396357846eae1e2f9b", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:@nestjs/axios"]}, {"file": "libs/recruitment-db/src/lib/user-assignments/dto/user-assignments.dto.ts", "hash": "3aa1b457843ee947c61998b7df157839d9f0f3d9", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/user-assignments/user-assignments.model.ts", "hash": "662c15e41708cda20879974ddc006ad43884ef2c", "deps": ["npm:sequelize-typescript"]}, {"file": "libs/recruitment-db/src/lib/user-assignments/user-assignments.module.ts", "hash": "2883d324ffb0892c08a7735b02dda4bedf9b6af3", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/user-assignments/user-assignments.service.ts", "hash": "463672f096aef42983c1cd8621fe8871db2c4d6b", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/users/dto/change-password.dto.ts", "hash": "f80ec7b7dfdd0775d50bef3df92f4b63bfba94cd", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/users/dto/change-roles.dto.ts", "hash": "b15feb2ecfd2f82fd8a106f450b55903ed5553f3", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/users/dto/create-user.dto.ts", "hash": "26b59ce0633fc0b74f012db99eeade4ba978eed3", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/users/dto/default-user.dto.ts", "hash": "b8cebbe3fd5fdb47e73ecf8760d227719acca573", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/users/dto/update-user.dto.ts", "hash": "4ff2fdcad93ef9b89b92b1e6448ffff1dcabf48b", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/users/users.model.ts", "hash": "b0206218ec1eb431f866df7d26dda35d5b4a4298", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/users/users.module.ts", "hash": "05b6d75f2e2f78b3ca783fae23853bb5dc43938c", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "auth", "email"]}, {"file": "libs/recruitment-db/src/lib/users/users.service.ts", "hash": "1880049b6e4b45f5590584e17c36b9f5e51e903d", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:bcrypt", "auth", "npm:sequelize", "email"]}, {"file": "libs/recruitment-db/src/lib/utils/fillDummyData.ts", "hash": "4d6df94725e9ec7cfa46ca31e9041540b0edab9e"}, {"file": "libs/recruitment-db/src/lib/workflow-chatbot/workflow-chatbot.module.ts", "hash": "6d3db75c399da36d9e0e2466487d540e6ab542e9", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "integrations", "npm:@nestjs/axios"]}, {"file": "libs/recruitment-db/src/lib/workflow-chatbot/workflow-chatbot.service.ts", "hash": "8b755e6ed2e03e1becfaeaf01fb137becf8aaa0b", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:@langchain/core", "npm:@langchain/openai", "npm:@qdrant/qdrant-js", "integrations", "npm:@nestjs/axios", "npm:uuid"]}, {"file": "libs/recruitment-db/src/lib/workflow/dto/edit-workflow.dto.ts", "hash": "24f6dfeef031719397b07919e6c0c3e93d45518c", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/workflow/dto/workflow.dto.ts", "hash": "299f6e4f953ebdc9057d8c66c9d4f210ec4d31a8", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/workflow/dto/workflowFilter.dto.ts", "hash": "e3d0978fa7c24438cdbc21bc33d5e0e329f255d2", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/recruitment-db/src/lib/workflow/workflow.model.ts", "hash": "2177b3678e5951443ef3cde740e9221ba4f61578", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/recruitment-db/src/lib/workflow/workflow.module.ts", "hash": "cc79b2b66724a002f617994a3cfc8ad3e094488e", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/recruitment-db/src/lib/workflow/workflow.service.ts", "hash": "ec31bc2d8c849b7bc25411ce9bd26d1563404519", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize"]}, {"file": "libs/recruitment-db/tsconfig.json", "hash": "62ebbd946474cea997e774d20fffc4d585c184f3"}, {"file": "libs/recruitment-db/tsconfig.lib.json", "hash": "030dc8c8374ad9564038694332f9d864c54b05c4"}, {"file": "libs/recruitment-db/tsconfig.spec.json", "hash": "67f149c4c04b9fad28e3b59462e55ba2d1e1d09b"}]}}, "code-executor": {"name": "code-executor", "type": "lib", "data": {"root": "libs/code-executor", "sourceRoot": "libs/code-executor/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/code-executor/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/code-executor"], "options": {"jestConfig": "libs/code-executor/jest.config.js", "passWithNoTests": true}}}, "tags": [], "files": [{"file": "libs/code-executor/.babelrc", "hash": "cf7ddd99c615a064ac18eb3109eee4f394ab1faf"}, {"file": "libs/code-executor/.eslintrc.json", "hash": "9d9c0db55bb1e91c5f2e7b64a02bc6bf69fc7cb5"}, {"file": "libs/code-executor/jest.config.js", "hash": "f0900437d497623c97d79149e1c09fc7861edce9"}, {"file": "libs/code-executor/project.json", "hash": "71127d5b39aec4eaa6ae60ae19cbf6181796e2fc"}, {"file": "libs/code-executor/README.md", "hash": "6c84eeacbff8bfff68a1f9440e3575fce325e5d9"}, {"file": "libs/code-executor/src/index.ts", "hash": "6bd30faae107db61c12a4fcc13ec87400fab4048"}, {"file": "libs/code-executor/src/lib/code-executor.module.ts", "hash": "7f3d50cc91f396d3af3f064e0b4554e8f980ac3d", "deps": ["npm:@nestjs/axios", "npm:@nestjs/common", "npm:@nestjs/config"]}, {"file": "libs/code-executor/src/lib/code-executor.service.spec.ts", "hash": "2d834932ab5fd8fdc2b830d632859840bdb65793", "deps": ["npm:@nestjs/testing"]}, {"file": "libs/code-executor/src/lib/code-executor.service.ts", "hash": "8fb5222b4c8d72e60a04290b01248161f091a871", "deps": ["npm:@nestjs/common", "npm:undici"]}, {"file": "libs/code-executor/src/lib/openai-executor.service.ts", "hash": "ded046eba0ea420482a89d2c01a988565ce9a7d8", "deps": ["npm:@nestjs/common", "npm:@nestjs/axios", "npm:rxjs", "npm:@nestjs/config"]}, {"file": "libs/code-executor/src/lib/submissions.types.ts", "hash": "393d2e705c092bae798a200f3b67e3ad89c973d1"}, {"file": "libs/code-executor/tsconfig.json", "hash": "62ebbd946474cea997e774d20fffc4d585c184f3"}, {"file": "libs/code-executor/tsconfig.lib.json", "hash": "030dc8c8374ad9564038694332f9d864c54b05c4"}, {"file": "libs/code-executor/tsconfig.spec.json", "hash": "67f149c4c04b9fad28e3b59462e55ba2d1e1d09b"}]}}, "hr-analytics": {"name": "hr-analytics", "type": "lib", "data": {"root": "libs/hr-analytics", "sourceRoot": "libs/hr-analytics/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/hr-analytics/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/hr-analytics"], "options": {"jestConfig": "libs/hr-analytics/jest.config.js", "passWithNoTests": true}}}, "tags": [], "files": [{"file": "libs/hr-analytics/.babelrc", "hash": "cf7ddd99c615a064ac18eb3109eee4f394ab1faf"}, {"file": "libs/hr-analytics/.eslintrc.json", "hash": "9d9c0db55bb1e91c5f2e7b64a02bc6bf69fc7cb5"}, {"file": "libs/hr-analytics/jest.config.js", "hash": "d94e154b04097b5165735a99f26f636e62399e15"}, {"file": "libs/hr-analytics/project.json", "hash": "c208b1acc217b90de250f7a052575d1f963ceaee"}, {"file": "libs/hr-analytics/README.md", "hash": "4cf6f951337614f2e2530f7d481570ce147a9306"}, {"file": "libs/hr-analytics/src/consts.ts", "hash": "5c16b7dc6f28dc41fbb4c986afb28dbf46df2014"}, {"file": "libs/hr-analytics/src/index.ts", "hash": "e08d73b9556b54c9cb138de88dedc071ee28b144"}, {"file": "libs/hr-analytics/src/lib/assessments-analytics/assessments-analytics.module.ts", "hash": "15079f82cab3a53e8371a85f1cea98c283e665c7", "deps": ["npm:@nestjs/common"]}, {"file": "libs/hr-analytics/src/lib/assessments-analytics/assessments-analytics.query.ts", "hash": "7d7f051c2b29a820d095498497bfa27cf7be9030"}, {"file": "libs/hr-analytics/src/lib/assessments-analytics/assessments-analytics.service.ts", "hash": "4e997957eb0829e01209ff3ccf5908064b3634ae", "deps": ["npm:@nestjs/common", "npm:moment"]}, {"file": "libs/hr-analytics/src/lib/jobs-analytics/jobs-analytics.module.ts", "hash": "fcfb04566a569ddfb098787bd16a627f594b773b", "deps": ["npm:@nestjs/common"]}, {"file": "libs/hr-analytics/src/lib/jobs-analytics/jobs-analytics.query.ts", "hash": "1d012fb04ad53c0c62839b53537b3ea4f2bc8d70"}, {"file": "libs/hr-analytics/src/lib/jobs-analytics/jobs-analytics.service.ts", "hash": "228b8573c7e36e77d9f58071e8a9ba002402c0c8", "deps": ["npm:@nestjs/common", "npm:@opensearch-project/opensearch", "npm:moment"]}, {"file": "libs/hr-analytics/src/lib/opensearch/opensearch.module.ts", "hash": "dc557f2b39d14b0f3c344f37cfc33d017954c734", "deps": ["npm:@nestjs/common", "npm:@opensearch-project/opensearch"]}, {"file": "libs/hr-analytics/src/lib/opensearch/opensearch.service.ts", "hash": "4afcfd89620ddb2b094a0197d0f880bea53f06ba", "deps": ["npm:@nestjs/common", "npm:@opensearch-project/opensearch"]}, {"file": "libs/hr-analytics/src/lib/users-analytics/dto/members-analytics.dto.ts", "hash": "b6c5428f867f7f5b52da42ed499c5d39528063f8", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/hr-analytics/src/lib/users-analytics/users-analytics.module.ts", "hash": "7cd07b1e3f95f876ea45d8b126effa52befcecc7", "deps": ["npm:@nestjs/common"]}, {"file": "libs/hr-analytics/src/lib/users-analytics/users-analytics.query.ts", "hash": "82c911e004e8db5da107e8bdc856d6b5bd7c8ac1", "deps": ["npm:rxjs"]}, {"file": "libs/hr-analytics/src/lib/users-analytics/users-analytics.service.ts", "hash": "ae2dbc4fd5291fe3cc9279ebf33517ab2e073036", "deps": ["npm:@nestjs/common", "npm:@opensearch-project/opensearch", "npm:moment"]}, {"file": "libs/hr-analytics/src/lib/utils/getDateOfJoiningRanges.ts", "hash": "d049632a0a33cf86d077692b501d8ea53ac0e47b", "deps": ["npm:moment"]}, {"file": "libs/hr-analytics/tsconfig.json", "hash": "62ebbd946474cea997e774d20fffc4d585c184f3"}, {"file": "libs/hr-analytics/tsconfig.lib.json", "hash": "030dc8c8374ad9564038694332f9d864c54b05c4"}, {"file": "libs/hr-analytics/tsconfig.spec.json", "hash": "67f149c4c04b9fad28e3b59462e55ba2d1e1d09b"}]}}, "integrations": {"name": "integrations", "type": "lib", "data": {"root": "libs/integrations", "sourceRoot": "libs/integrations/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/integrations/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/integrations"], "options": {"jestConfig": "libs/integrations/jest.config.js", "passWithNoTests": true}}}, "tags": [], "files": [{"file": "libs/integrations/.babelrc", "hash": "cf7ddd99c615a064ac18eb3109eee4f394ab1faf"}, {"file": "libs/integrations/.eslintrc.json", "hash": "9d9c0db55bb1e91c5f2e7b64a02bc6bf69fc7cb5"}, {"file": "libs/integrations/jest.config.js", "hash": "a3104964fb83392a47c4a26448582f6b92b7bfa8"}, {"file": "libs/integrations/project.json", "hash": "3228edf4c6b1da3bfe61d97b56647eb79c4ad0e6"}, {"file": "libs/integrations/README.md", "hash": "df99cf2ce2ef97c330b2381e2b7e71924a4f70dc"}, {"file": "libs/integrations/src/google/google.module.ts", "hash": "91441e67917023464ef2124569763a9af54f97d6", "deps": ["npm:@nestjs/common"]}, {"file": "libs/integrations/src/google/google.service.ts", "hash": "0b6ad0bb1e64099eabea71b96cdb512f942a7725", "deps": ["npm:@nestjs/common", "npm:@google-cloud/talent"]}, {"file": "libs/integrations/src/index.ts", "hash": "3283623a5c48ae36bd886a5987c9195b550f74c3"}, {"file": "libs/integrations/src/langchain/langchain.module.ts", "hash": "cbaa3a4b6d5c3d83479a89044bdf7dd88adfc2df", "deps": ["npm:@nestjs/common"]}, {"file": "libs/integrations/src/langchain/langchain.service.ts", "hash": "480618a0b965a2d3a5e488e5093954fefebf8dcf", "deps": ["npm:@nestjs/common", "npm:langchain", "npm:zod", "npm:@langchain/openai"]}, {"file": "libs/integrations/tsconfig.json", "hash": "62ebbd946474cea997e774d20fffc4d585c184f3"}, {"file": "libs/integrations/tsconfig.lib.json", "hash": "030dc8c8374ad9564038694332f9d864c54b05c4"}, {"file": "libs/integrations/tsconfig.spec.json", "hash": "67f149c4c04b9fad28e3b59462e55ba2d1e1d09b"}]}}, "calendars": {"name": "calendars", "type": "lib", "data": {"root": "libs/calendars", "sourceRoot": "libs/calendars/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/calendars/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/calendars"], "options": {"jestConfig": "libs/calendars/jest.config.js", "passWithNoTests": true}}}, "tags": [], "files": [{"file": "libs/calendars/.babelrc", "hash": "cf7ddd99c615a064ac18eb3109eee4f394ab1faf"}, {"file": "libs/calendars/.eslintrc.json", "hash": "9d9c0db55bb1e91c5f2e7b64a02bc6bf69fc7cb5"}, {"file": "libs/calendars/jest.config.js", "hash": "11e8dad272ca564520cbbdbb5da0748c00a9c2ff"}, {"file": "libs/calendars/project.json", "hash": "0f2edaaf4591ff80e88fd0d000e98acca7713062"}, {"file": "libs/calendars/README.md", "hash": "39b34054bd8d09870027004152c2a19e69cc4be4"}, {"file": "libs/calendars/src/index.ts", "hash": "bc0c43ac9ef0ed38023b88a91aeff6fa23f62243"}, {"file": "libs/calendars/src/lib/calendars.module.ts", "hash": "f77156e0cd92d873aa5edc0a4352e00317a279a1", "deps": ["recruitment-db", "npm:@nestjs/common", "db", "npm:@nestjs/axios"]}, {"file": "libs/calendars/src/lib/calendars.service.spec.ts", "hash": "5a63babbb9f29159ab4bc5158983e4c1c1cef3b8", "deps": ["npm:@nestjs/testing"]}, {"file": "libs/calendars/src/lib/dto/create-event.dto.ts", "hash": "d0425210a3b02e5fff12a210037dbbde11ab5453", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/calendars/src/lib/dto/get-free-slots.dto.ts", "hash": "29ddcc7ee16e81b0d74a652519d3a54398a504b0", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/calendars/src/lib/googleAuth.service.ts", "hash": "7f3b99b00b3965a4d5cbe8a3dd04a6d29610d9d3", "deps": ["recruitment-db", "npm:@nestjs/common", "npm:googlea<PERSON>", "npm:@nestjs/axios", "npm:uuid", "temporal"]}, {"file": "libs/calendars/src/lib/msGraph.service.ts", "hash": "65cb366a5eed2780e88627b18eff486c47aca450", "deps": ["npm:@nestjs/common", "npm:@microsoft/microsoft-graph-client", "npm:@azure/msal-node", "recruitment-db", "npm:cross-fetch", "npm:@nestjs/axios", "npm:uuid", "npm:isomorphic-fetch"]}, {"file": "libs/calendars/src/lib/utility.service.ts", "hash": "2f9589436fe89ff09d7bc3ebe8a5e8fc952b1079", "deps": ["npm:@nestjs/common", "recruitment-db"]}, {"file": "libs/calendars/tsconfig.json", "hash": "62ebbd946474cea997e774d20fffc4d585c184f3"}, {"file": "libs/calendars/tsconfig.lib.json", "hash": "030dc8c8374ad9564038694332f9d864c54b05c4"}, {"file": "libs/calendars/tsconfig.spec.json", "hash": "67f149c4c04b9fad28e3b59462e55ba2d1e1d09b"}]}}, "temporal": {"name": "temporal", "type": "app", "data": {"root": "apps/temporal", "sourceRoot": "apps/temporal/src", "projectType": "application", "targets": {"build": {"executor": "@nrwl/node:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/temporal", "main": "apps/temporal/src/main.ts", "tsConfig": "apps/temporal/tsconfig.app.json", "assets": ["apps/temporal/src/assets", "apps/temporal/src/app/certs"]}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/temporal/src/environments/environment.ts", "with": "apps/temporal/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nrwl/node:execute", "options": {"buildTarget": "temporal:build"}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/temporal/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/apps/temporal"], "options": {"jestConfig": "apps/temporal/jest.config.js", "passWithNoTests": true}}}, "tags": [], "files": [{"file": "apps/temporal/.eslintrc.json", "hash": "9d9c0db55bb1e91c5f2e7b64a02bc6bf69fc7cb5"}, {"file": "apps/temporal/jest.config.js", "hash": "a9ff569437729dc903c57b4a4e4597fcc9953bf0"}, {"file": "apps/temporal/project.json", "hash": "7c15522a58866f6102eee98816169676555b7ffd"}, {"file": "apps/temporal/src/app/.gitkeep", "hash": "e69de29bb2d1d6434b8b29ae775ad8c2e48c5391"}, {"file": "apps/temporal/src/app/app.controller.spec.ts", "hash": "e556f34a35fe8bf5523c987c3c5ac5072ebf7ab7", "deps": ["npm:@nestjs/testing"]}, {"file": "apps/temporal/src/app/app.controller.ts", "hash": "5b53d52694aabc10dc5fd4ed480ea7ad0d61ae31", "deps": ["npm:@nestjs/common"]}, {"file": "apps/temporal/src/app/app.module.ts", "hash": "972b4b2c938f7c6664ab27829500d4f1b8cb6bef", "deps": ["npm:@nestjs/common"]}, {"file": "apps/temporal/src/app/app.service.spec.ts", "hash": "27132d3b845b325df23b2be3e004c9a06d5a04bf", "deps": ["npm:@nestjs/testing"]}, {"file": "apps/temporal/src/app/app.service.ts", "hash": "587fe0871cc1758c0f4b8022f79c94c16521e554", "deps": ["npm:@nestjs/common"]}, {"file": "apps/temporal/src/app/certs/ca.key", "hash": "936af09d04755bd8f40a38ed9ff39ec37f1eb475"}, {"file": "apps/temporal/src/app/certs/ca.pem", "hash": "e83fce818461e2f114ef14c32bbbc8e900a3cfc4"}, {"file": "apps/temporal/src/app/certs/client.key", "hash": "584f1dd5692d17e4d1b9f9a513e5d159b8ab7ab5"}, {"file": "apps/temporal/src/app/certs/client.pem", "hash": "7a06aba6eba869b0d291bd1d9eee43f014ec6999"}, {"file": "apps/temporal/src/app/workflow/activities/activities.module.ts", "hash": "beca17dd94907e631745776ca28dcee21da6a761", "deps": ["npm:@nestjs/common", "email", "npm:@nestjs/axios"]}, {"file": "apps/temporal/src/app/workflow/activities/activities.service.ts", "hash": "217f944734113ecca4c29c7ba478a0b4c9ce3376", "deps": ["npm:@nestjs/common", "email", "npm:@nestjs/axios"]}, {"file": "apps/temporal/src/app/workflow/caKeys.ts", "hash": "70e5827b6613ce58a91bf883dcc53e6960687ef2"}, {"file": "apps/temporal/src/app/workflow/dto/create-test-manager.dto.ts", "hash": "1134a55e8e041a178d250d02febbfa5ff60bd7c8", "deps": ["npm:@nestjs/swagger", "npm:class-validator", "npm:sequelize-typescript"]}, {"file": "apps/temporal/src/app/workflow/enum/round.enum.ts", "hash": "e2c7043cc1965d27449b07364a6b10f11dc17dab"}, {"file": "apps/temporal/src/app/workflow/temporal-certs/ca.key", "hash": "bc9d3b616da649e99bc83d803887550eed2da486"}, {"file": "apps/temporal/src/app/workflow/temporal-certs/ca.pem", "hash": "24eaa77569ad17963333a2a9624fabdca4a582ad"}, {"file": "apps/temporal/src/app/workflow/temporal-certs/client.key", "hash": "26ab600d3b8e144ba0d867297703a447c9245347"}, {"file": "apps/temporal/src/app/workflow/temporal-certs/client.pem", "hash": "fe622eb09258140bf017e72047a0679c74b18684"}, {"file": "apps/temporal/src/app/workflow/temporal-client/temporalClient.module.ts", "hash": "fa602abb6ba42e83b9211915c86f1d7edee7c7f3", "deps": ["npm:@nestjs/common", "npm:@nestjs/axios"]}, {"file": "apps/temporal/src/app/workflow/temporal-client/temporalClient.services.ts", "hash": "04e9dfe0d87979d06ee4c4b0baba87c2e4f02258", "deps": ["npm:@nestjs/axios", "npm:@nestjs/common"]}, {"file": "apps/temporal/src/app/workflow/temporal/built-workflow-bundle.ts", "hash": "d59a1421a9dcc1b09788b74bbe5ebcd6643cdc63", "deps": ["npm:@temporalio/worker"]}, {"file": "apps/temporal/src/app/workflow/temporal/workflowCode.ts", "hash": "e3d1b968cf854bcc8b59538ee3c47f60cbacce76"}, {"file": "apps/temporal/src/app/workflow/temporal/workflows.ts", "hash": "1f30c9bdcb5d75157f7c2626e5268923142b3c52", "deps": ["npm:@temporalio/workflow"]}, {"file": "apps/temporal/src/app/workflow/worker.provider.ts", "hash": "6e4faabde4b5d427e12498dfbe200833814d4b13", "deps": ["npm:@temporalio/worker", "email"]}, {"file": "apps/temporal/src/app/workflow/workflow.provider.ts", "hash": "22a86a5397c09b84c2a42a69fa00dd9dab2e6c87", "deps": ["npm:@nestjs/common", "npm:@temporalio/client"]}, {"file": "apps/temporal/src/assets/.gitkeep", "hash": "e69de29bb2d1d6434b8b29ae775ad8c2e48c5391"}, {"file": "apps/temporal/src/environments/environment.prod.ts", "hash": "c9669790be176ac85a5d8c11278875c2f52dc507"}, {"file": "apps/temporal/src/environments/environment.ts", "hash": "a20cfe55731540eac839ab33f9ce1eaa6da50b16"}, {"file": "apps/temporal/src/main.ts", "hash": "1ffb50460141cc67f55452f8b9433cf3d569ccab", "deps": ["npm:@nestjs/common", "npm:@nestjs/core"]}, {"file": "apps/temporal/tsconfig.app.json", "hash": "515cb2d76f75cf59bd88a173bd9e3a336d6a6846"}, {"file": "apps/temporal/tsconfig.json", "hash": "63dbe35fb282d5f9ac4a724607173e6316269e29"}, {"file": "apps/temporal/tsconfig.spec.json", "hash": "a18afb604688956c6a84dd7780d99923f0c04662"}]}}, "kinesis": {"name": "kinesis", "type": "lib", "data": {"root": "libs/kinesis", "sourceRoot": "libs/kinesis/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/kinesis/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/kinesis"], "options": {"jestConfig": "libs/kinesis/jest.config.js", "passWithNoTests": true}}}, "tags": [], "files": [{"file": "libs/kinesis/jest.config.js", "hash": "6b621668add1091996c9fb32c81ea6b765165961"}, {"file": "libs/kinesis/project.json", "hash": "c41fd5a3c238e60ce9695ce17fe80c1d0e61cc87"}, {"file": "libs/kinesis/README.md", "hash": "843a5ddf5da1127aefc163f21531da367fee83d4"}, {"file": "libs/kinesis/src/index.ts", "hash": "efcc9a898838fcbc451eb3dcbece83b51e16656a"}, {"file": "libs/kinesis/src/lib/kinesis.middleware.ts", "hash": "0a1f6e07d7f859a2e4d423927bb33c4b288b0c7a", "deps": ["npm:@nestjs/common"]}, {"file": "libs/kinesis/src/lib/utils/put-records.ts", "hash": "a6999c46b7c6d6ec399a2498a4d2f55afab56b98", "deps": ["npm:aws-sdk"]}, {"file": "libs/kinesis/src/lib/utils/streamEnvs.ts", "hash": "931c5433ef27428f69e22a1f4878d0a33157c0a1"}, {"file": "libs/kinesis/tsconfig.json", "hash": "62ebbd946474cea997e774d20fffc4d585c184f3"}, {"file": "libs/kinesis/tsconfig.lib.json", "hash": "6efdbeecb5481388fd79af80e5825e38c25bc038"}, {"file": "libs/kinesis/tsconfig.spec.json", "hash": "67f149c4c04b9fad28e3b59462e55ba2d1e1d09b"}]}}, "qdrant": {"name": "qdrant", "type": "lib", "data": {"root": "libs/qdrant", "sourceRoot": "libs/qdrant/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/qdrant/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/qdrant"], "options": {"jestConfig": "libs/qdrant/jest.config.js", "passWithNoTests": true}}}, "tags": [], "files": [{"file": "libs/qdrant/.babelrc", "hash": "cf7ddd99c615a064ac18eb3109eee4f394ab1faf"}, {"file": "libs/qdrant/.eslintrc.json", "hash": "9d9c0db55bb1e91c5f2e7b64a02bc6bf69fc7cb5"}, {"file": "libs/qdrant/jest.config.js", "hash": "e77c8b0d174eb6f08383182709e92d8b6deb4e38"}, {"file": "libs/qdrant/project.json", "hash": "3d7240aea911fd809dbb9389f3fa274cd600e40f"}, {"file": "libs/qdrant/README.md", "hash": "0cab05bdfd0257c6f8baaca4297858cd427bcbc5"}, {"file": "libs/qdrant/src/index.ts", "hash": "e55fd25a1a32bf7c7ae13ba59194dc691cebe9dc"}, {"file": "libs/qdrant/src/lib/qdrant.middleware.ts", "hash": "f64349743d6bbcf261a71978b67fc1d5a2f9b265", "deps": ["npm:@nestjs/common"]}, {"file": "libs/qdrant/src/lib/utils/put-records.ts", "hash": "95153ae20955cf3c0d72c0cb9ad468366785e2f5", "deps": ["npm:aws-sdk", "npm:@qdrant/qdrant-js", "integrations"]}, {"file": "libs/qdrant/tsconfig.json", "hash": "62ebbd946474cea997e774d20fffc4d585c184f3"}, {"file": "libs/qdrant/tsconfig.lib.json", "hash": "6efdbeecb5481388fd79af80e5825e38c25bc038"}, {"file": "libs/qdrant/tsconfig.spec.json", "hash": "67f149c4c04b9fad28e3b59462e55ba2d1e1d09b"}]}}, "twilio": {"name": "twi<PERSON>", "type": "lib", "data": {"root": "libs/twilio", "sourceRoot": "libs/twilio/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/twilio/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/twilio"], "options": {"jestConfig": "libs/twilio/jest.config.js", "passWithNoTests": true}}}, "tags": [], "files": [{"file": "libs/twilio/.babelrc", "hash": "cf7ddd99c615a064ac18eb3109eee4f394ab1faf"}, {"file": "libs/twilio/.eslintrc.json", "hash": "9d9c0db55bb1e91c5f2e7b64a02bc6bf69fc7cb5"}, {"file": "libs/twilio/jest.config.js", "hash": "4d8dad8028d880f3c6edb927611beb5c5ae45fb1"}, {"file": "libs/twilio/project.json", "hash": "bd718dff017f617010f487e54486a0d3c4635a8f"}, {"file": "libs/twilio/README.md", "hash": "75cc961a939af3c88c750d6575f32be642c08be1"}, {"file": "libs/twilio/src/index.ts", "hash": "716431105184fe446ca9756729c132e4de10eed2"}, {"file": "libs/twilio/src/lib/twilio.conversation.service.ts", "hash": "aae85041e20a02ce71d4ffbc7c021bd9ff34a4fc", "deps": ["npm:@nestjs/common", "npm:twi<PERSON>"]}, {"file": "libs/twilio/src/lib/twilio.module.ts", "hash": "883602863d7fa8e916314358c3c0a9d5ece15249", "deps": ["npm:@nestjs/common", "npm:@nestjs/axios", "db", "recruitment-db"]}, {"file": "libs/twilio/src/lib/twilio.service.spec.ts", "hash": "f81669b2f1eb3deb7ad1267df25ce9ef476d34f8", "deps": ["npm:@nestjs/testing"]}, {"file": "libs/twilio/src/lib/twilio.video.service.ts", "hash": "28e1b7e1de48d8a6d0718b07a8acb7fc2beffbcc", "deps": ["npm:@nestjs/common", "npm:@nestjs/axios", "npm:twi<PERSON>", "npm:uuid", "recruitment-db", "temporal"]}, {"file": "libs/twilio/tsconfig.json", "hash": "62ebbd946474cea997e774d20fffc4d585c184f3"}, {"file": "libs/twilio/tsconfig.lib.json", "hash": "030dc8c8374ad9564038694332f9d864c54b05c4"}, {"file": "libs/twilio/tsconfig.spec.json", "hash": "67f149c4c04b9fad28e3b59462e55ba2d1e1d09b"}]}}, "email": {"name": "email", "type": "lib", "data": {"root": "libs/email", "sourceRoot": "libs/email/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/email/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/email"], "options": {"jestConfig": "libs/email/jest.config.js", "passWithNoTests": true}}}, "tags": [], "files": [{"file": "libs/email/.babelrc", "hash": "cf7ddd99c615a064ac18eb3109eee4f394ab1faf"}, {"file": "libs/email/.eslintrc.json", "hash": "9d9c0db55bb1e91c5f2e7b64a02bc6bf69fc7cb5"}, {"file": "libs/email/jest.config.js", "hash": "57d08ebcee6f130c911e7ff59247dd9417983e6a"}, {"file": "libs/email/project.json", "hash": "a0594436f00358a44ea446462967ff4158b306d9"}, {"file": "libs/email/README.md", "hash": "0dc2ba820097d537acd526527c2b7a6b8ecc0c45"}, {"file": "libs/email/src/index.ts", "hash": "057f2e3aa5774af48b72dd68bb42753d97542791"}, {"file": "libs/email/src/lib/email.module.ts", "hash": "611bd8a107f04a20430d245d16491d5a3a460576", "deps": ["npm:@nestjs/common", "npm:@nestjs/config", "npm:@nestjs/axios"]}, {"file": "libs/email/src/lib/email.resolver.service.ts", "hash": "5a50998a01f28167d469c269164c176ce3aee839", "deps": ["npm:@nestjs/common"]}, {"file": "libs/email/src/lib/email.service.spec.ts", "hash": "1031a5cecc72f0117650bd6add851133922c4151", "deps": ["npm:@nestjs/testing"]}, {"file": "libs/email/src/lib/email.service.ts", "hash": "51dff33494bc1cedbc3a6cae71583e4213d03220", "deps": ["npm:@nestjs/common", "npm:@sendgrid/mail", "npm:nodemailer"]}, {"file": "libs/email/src/lib/templates/jobDetail.ts", "hash": "d45902a5badcf57642ce9f281bde48d708bc5e4f"}, {"file": "libs/email/tsconfig.json", "hash": "62ebbd946474cea997e774d20fffc4d585c184f3"}, {"file": "libs/email/tsconfig.lib.json", "hash": "030dc8c8374ad9564038694332f9d864c54b05c4"}, {"file": "libs/email/tsconfig.spec.json", "hash": "67f149c4c04b9fad28e3b59462e55ba2d1e1d09b"}]}}, "auth": {"name": "auth", "type": "lib", "data": {"root": "libs/auth", "sourceRoot": "libs/auth/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/auth/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/auth"], "options": {"jestConfig": "libs/auth/jest.config.js", "passWithNoTests": true}}}, "tags": [], "files": [{"file": "libs/auth/.babelrc", "hash": "cf7ddd99c615a064ac18eb3109eee4f394ab1faf"}, {"file": "libs/auth/.eslintrc.json", "hash": "9d9c0db55bb1e91c5f2e7b64a02bc6bf69fc7cb5"}, {"file": "libs/auth/jest.config.js", "hash": "6678ba68f9f31a7787ebe743d161e67899531a65"}, {"file": "libs/auth/project.json", "hash": "d6034430c66315c75b5a06550445a4c5482326fa"}, {"file": "libs/auth/README.md", "hash": "910a0e968f9f038cf0b8c47114caafa34c204a85"}, {"file": "libs/auth/src/index.ts", "hash": "927be83dccf3e340f05a09d5e731de32bbc9fa28"}, {"file": "libs/auth/src/lib/auth.service.spec.ts", "hash": "1e8e5bb2579dc10863e6b06087c03028f3480d9a", "deps": ["npm:@nestjs/testing"]}, {"file": "libs/auth/src/lib/auth0.module.ts", "hash": "8949ecf6f75801e69db8ceb5a7095f68d3ff0127", "deps": ["npm:@nestjs/common", "npm:@nestjs/passport"]}, {"file": "libs/auth/src/lib/auth0.service.ts", "hash": "02f2f9b1a940851bd3617fc15c1e5095832d3936", "deps": ["npm:@nestjs/common", "npm:cross-fetch"]}, {"file": "libs/auth/src/lib/authz/jwt.strategy.ts", "hash": "cbede9ea0478f37963741853377f8190ce3c7ee4", "deps": ["npm:@nestjs/common", "npm:@nestjs/passport", "npm:passport-jwt", "npm:jwks-rsa", "npm:dotenv"]}, {"file": "libs/auth/src/lib/dto/create-auth-candidate.dto.ts", "hash": "62401779547020812b798c0b801f352e3e728439", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/auth/src/lib/dto/create-auth-company.dto.ts", "hash": "4bf83477e4b7d47e503896acb28ca4d8c41937b0", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/auth/src/lib/dto/update-user.dto.ts", "hash": "50aa2d16708e3386e127e5e7d627c39938a61fa7", "deps": ["npm:@nestjs/swagger"]}, {"file": "libs/auth/src/lib/dto/user.dto.ts", "hash": "058b520f100713aa01994068c54bfa9d82b4a4cf", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/auth/src/lib/permissions/permissions.decorator.ts", "hash": "56a5e20f8f50e9283ef08dea5040ae97ed35e8d7", "deps": ["npm:@nestjs/common"]}, {"file": "libs/auth/src/lib/permissions/permissions.guard.ts", "hash": "d7738677e50781faab359bb08011015155bb4e57", "deps": ["npm:@nestjs/common", "npm:rxjs", "npm:@nestjs/core"]}, {"file": "libs/auth/src/lib/user.decorator.ts", "hash": "9e438fc5dd81e8785a87e5a7846da5e8d84fb66c", "deps": ["npm:@nestjs/common"]}, {"file": "libs/auth/tsconfig.json", "hash": "62ebbd946474cea997e774d20fffc4d585c184f3"}, {"file": "libs/auth/tsconfig.lib.json", "hash": "030dc8c8374ad9564038694332f9d864c54b05c4"}, {"file": "libs/auth/tsconfig.spec.json", "hash": "67f149c4c04b9fad28e3b59462e55ba2d1e1d09b"}]}}, "db": {"name": "db", "type": "lib", "data": {"root": "libs/db", "sourceRoot": "libs/db/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/db/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/db"], "options": {"jestConfig": "libs/db/jest.config.js", "passWithNoTests": true}}}, "tags": [], "files": [{"file": "libs/db/.babelrc", "hash": "cf7ddd99c615a064ac18eb3109eee4f394ab1faf"}, {"file": "libs/db/.eslintrc.json", "hash": "9d9c0db55bb1e91c5f2e7b64a02bc6bf69fc7cb5"}, {"file": "libs/db/jest.config.js", "hash": "0c5069fa9dfbdce6a4d50fbcb5ae5ca83dabe68e"}, {"file": "libs/db/project.json", "hash": "9a1faaa52f5800a5ff4fe7e02103d50e40193a6b"}, {"file": "libs/db/README.md", "hash": "172ab7b03a394b04092845e339ac42174ec3ff48"}, {"file": "libs/db/src/index.ts", "hash": "96027aba79463dc97474aec392d1e606b52ad55a"}, {"file": "libs/db/src/lib/assessment-chatbot/assessmentChatbot.model.ts", "hash": "78d71f3e401990830015682bb79851784135818b", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/db/src/lib/assessment-chatbot/assessmentChatbot.module.ts", "hash": "7c2c38c02ee800317bf219302e2258e75396201c", "deps": ["npm:@nestjs/common", "npm:@nestjs/config", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/assessment-chatbot/assessmentChatbot.service.ts", "hash": "aa7daa578a971c99629731c818478b7718bb33b1", "deps": ["npm:@nestjs/common", "npm:@nestjs/config", "npm:@langchain/openai", "npm:@langchain/core"]}, {"file": "libs/db/src/lib/assessment-chatbot/assessmentChatbot.types.ts", "hash": "c3841a34ea6565e8b58807ba3f2cf43bc57c0a42"}, {"file": "libs/db/src/lib/assessment-database/assessmentDatabase.model.ts", "hash": "4696061783bab563f465b796541b8cfdee81c776", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/db/src/lib/assessment-database/assessmentDatabase.module.ts", "hash": "b9fcd99e6a09f4d67efb2b8c0c578b13c5a4880e", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/assessment-database/assessmentDatabase.service.ts", "hash": "a52e5e8fadce02446767a3e3d917264e87db1774", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/assignment/assignment.model.ts", "hash": "e6d306fd4ad4d31b248b21269c217424ed05e52e", "deps": ["npm:@nestjs/swagger", "npm:sequelize-typescript"]}, {"file": "libs/db/src/lib/assignment/assignment.module.ts", "hash": "d06e2065252334346723de485237e8e84747d9d3", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/assignment/assignment.service.ts", "hash": "d3e2893921fa8e88999f8858b55be19299a10470", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize"]}, {"file": "libs/db/src/lib/assignment/assignment.types.ts", "hash": "9ab4645b0794ba9f1765e751fc49c1b437023909", "deps": ["npm:class-validator"]}, {"file": "libs/db/src/lib/assignment/dto/get-assignment.dto.ts", "hash": "e990adf6441228bdcf12ab0695a46fa6b3e1153a", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/db/src/lib/assignment/dto/update-assignment.dto.ts", "hash": "d3238eb89601583befd3f5f9984fdc6d36fdc359", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/db/src/lib/calendar/calendar.module.ts", "hash": "7614711a387b2c6dacc423630e65314a6bd468c9", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/calendar/calendar.service.ts", "hash": "fc0c5fcb7e1b1e46dbb2845d5444b03ed545b3eb", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:moment", "npm:sequelize"]}, {"file": "libs/db/src/lib/calendar/dto/create-event.dto.ts", "hash": "a38616919233d873fc375ca9bed60c2909651cc7", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/db/src/lib/calendar/dto/delete-event.dto.ts", "hash": "bb549cdf9e4704b3cd2e5a2bacd61554f1cf2034", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/db/src/lib/calendar/dto/update-event.dto.ts", "hash": "53be5cafea4090c013d96886d129db294d04c7ab", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/db/src/lib/calendar/event-user.model.ts", "hash": "4bd1b6428fc32d4f5a9528dd207ad8e92615c350", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/db/src/lib/calendar/event.model.ts", "hash": "07f074d67f1f088adeb14faab991d1510307c3a9", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/db/src/lib/code-execution/code-execution.model.ts", "hash": "88ace7ced82a94264e76b83df67627a75faeb543", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/db/src/lib/code-execution/code-execution.module.ts", "hash": "87b4c194eaeef545892cc13972f2b0d2e80d8db4", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/code-execution/code-execution.service.ts", "hash": "c00db5e9fd901de9977366ac287c5896dba24386", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/coding-area/coding-area.model.ts", "hash": "25e946648c433752dbbcb6e74a6152998f8dd701", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/db/src/lib/coding-area/coding-area.module.ts", "hash": "d5a187e0ff8616aa972eeb0e0e7bc1cfd9b1cd39", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/coding-area/coding-area.service.ts", "hash": "57dfff3e7251386de984c0d345db51fed475da5f", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/db.module.ts", "hash": "08f7c772a97a5d9272b6e2b82c51050db36767a1", "deps": ["npm:@nestjs/common", "npm:@nestjs/config", "npm:@nestjs/sequelize", "email"]}, {"file": "libs/db/src/lib/db.service.spec.ts", "hash": "f4908f2462408b307f04ff693a7e4a5c0f988111", "deps": ["npm:@nestjs/testing"]}, {"file": "libs/db/src/lib/deadline/deadline.model.ts", "hash": "b6a58146e4ec13409367658fc179247640a74438", "deps": ["npm:@nestjs/swagger", "npm:sequelize-typescript"]}, {"file": "libs/db/src/lib/domain-questions/domain-assessment.model.ts", "hash": "92fbce13fa4994aab429ca8fd1877bb7b702b3bc", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/db/src/lib/domain-questions/domain-questions.model.ts", "hash": "ae0d8fdf9018d6f9129ba585c5bd18e8e4d5a3c9", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/db/src/lib/domain-questions/domain-questions.module.ts", "hash": "9a0f3233341b5853fb66676b3ef35033258bc1a2", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/domain-questions/domain-questions.service.ts", "hash": "7336467f6b42f5c315b5c756b74b3d266132e915", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize", "qdrant"]}, {"file": "libs/db/src/lib/domain-questions/dto/domain-filter.dto.ts", "hash": "318492d72a3914bedeec590433635e096b992ee0", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/db/src/lib/domain-result/domain-answer.model.ts", "hash": "13a2078eac830e6e8660983eccba1c31fb2bf60e", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/db/src/lib/domain-result/domain-result.model.ts", "hash": "c59da1d2de8a8a25136020447b0a3867b77d09f4", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/db/src/lib/domain-result/domain-result.module.ts", "hash": "8bdfbf4c67509ad36b6932b215acc918d8011bd2", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/domain-result/domain-result.service.ts", "hash": "43c2d06c413b9d88f15dfa9108ff41a442ee7548", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize"]}, {"file": "libs/db/src/lib/googleAuth/googleAuth.model.ts", "hash": "90c15fd6a369fbc16e77a33441e5dab4cfd36357", "deps": ["npm:@nestjs/swagger", "npm:sequelize-typescript"]}, {"file": "libs/db/src/lib/googleAuth/googleAuth.module.ts", "hash": "a12b94d12676ac83f4eebd49b2ad77c44e14d407", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/googleAuth/googleAuth.service.ts", "hash": "cb0abc961f7b22f51e2fa6e7355ebb080affbdea", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/languages/languages.model.ts", "hash": "2d14d6508c25724b201cd6b54d79b6dcb6f612c9", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/db/src/lib/languages/languages.module.ts", "hash": "836efe3d416722702e9f9ce0ffae62bc8d93ea40", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/languages/languages.service.ts", "hash": "ce59c647b828f6b9318689fdc0e7c8773c534f03", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/live-coding/filter.interface.ts", "hash": "8533ed5ef0c060952be5fe6bbf9b600bfd70b704"}, {"file": "libs/db/src/lib/live-coding/live-coding.dto.ts", "hash": "d0e8b2347c7040b37a943ba922cce3760e23778e", "deps": ["npm:class-validator"]}, {"file": "libs/db/src/lib/live-coding/live-coding.model.ts", "hash": "f34321030cc908819eacb86875fabfe4ef4a5740", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/db/src/lib/live-coding/live-coding.module.ts", "hash": "65d30edb34859c262e551728f95ec1ebbdda4392", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "email"]}, {"file": "libs/db/src/lib/live-coding/live-coding.service.ts", "hash": "8486fd3d3ffb43617488b36622611fb250e1e4b7", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:@convergence/jwt-util", "npm:sequelize", "npm:uuid", "npm:undici", "email", "qdrant"]}, {"file": "libs/db/src/lib/live-coding/live-drafts/live-drafts.model.ts", "hash": "95ddaee4496912044933e4aae2cf5857b08e39b8", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/db/src/lib/live-coding/live-drafts/live-drafts.module.ts", "hash": "31e1bf7353af1373ee55f61e2fd9abdddc3eeea0", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/live-coding/live-drafts/live-drafts.service.ts", "hash": "aa3a7e12fe1225e5b94add3f91027cbc4e533b02", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:uuid", "npm:sequelize"]}, {"file": "libs/db/src/lib/packages/packages.model.ts", "hash": "fc3c1bc8a416faef979d1ad123ffcc8297819823", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/db/src/lib/packages/packages.module.ts", "hash": "8fd9398fdcc80cd3fdd812ac87dbe9a307ecb617", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/questions/questions.model.ts", "hash": "bfcafeccdeba62838129704c10f351c44916033c", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/db/src/lib/questions/questions.module.ts", "hash": "1b616938ec03cadfdeb34d3352983b97f902b5bc", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/questions/questions.service.ts", "hash": "8ba93d829c47b58c380c4c4b71f49caf0fc720d5", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/reviewAndScore/reviewAndScore.model.ts", "hash": "d9e63446e53e34275e1253534955c7d84154b9cb", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/db/src/lib/reviewAndScore/reviewAndScore.module.ts", "hash": "3bbc6be1651796596ce7ecd77f2fa211d2cbea9e", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/reviewAndScore/reviewAndScore.service.ts", "hash": "73fd3846b5514b744e5a0f123610d6534339e174", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize"]}, {"file": "libs/db/src/lib/reviewAndScore/reviewFilter.interface.ts", "hash": "6a7c33f3180b10593fa9fb23be7b10927bcedcce"}, {"file": "libs/db/src/lib/take-home/home-drafts/home-drafts.model.ts", "hash": "d93a5a7c47ec192079177352aa730e0f543c376a", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/db/src/lib/take-home/home-drafts/home-drafts.module.ts", "hash": "18cc9a1424860ecc4ab655ab9c35ae8af1c191a2", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/take-home/home-drafts/home-drafts.service.ts", "hash": "15adf7a19bfb7c1dd016072203c8c3476157b1a4", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize", "npm:uuid"]}, {"file": "libs/db/src/lib/take-home/take-home-submission.model.ts", "hash": "b8c2302a78e8bb33510bfe0c1157a6700c28cc6f", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/db/src/lib/take-home/take-home-submission.module.ts", "hash": "e38054603afad0c19c62620fd942cbc7afe6f9d0", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/take-home/take-home-submission.service.spec.ts", "hash": "080504229881b9456abe97bc616325edbb305508", "deps": ["npm:@nestjs/testing", "npm:@nestjs/sequelize", "npm:@nestjs/common"]}, {"file": "libs/db/src/lib/take-home/take-home-submission.service.ts", "hash": "a4cbdc902256481cbab5083150e5baea6b2dbd34", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize"]}, {"file": "libs/db/src/lib/take-home/take-home-task.model.ts", "hash": "cec1a094053508694ab38126af076022d620ae87", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/db/src/lib/take-home/take-home-task.module.ts", "hash": "0c379dc470aa1051f2157d02e8f005752b663f3e", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/take-home/take-home-task.service.ts", "hash": "c1ba02488f040aa51b87e8cf5d5221b14aad8368", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize", "npm:sequelize", "npm:uuid", "qdrant"]}, {"file": "libs/db/src/lib/test-manager/dto/create-test-manager.dto.ts", "hash": "688d69f895641ab9a783fdefaf84c5478d85e399", "deps": ["npm:@nestjs/swagger", "npm:class-validator", "npm:sequelize-typescript"]}, {"file": "libs/db/src/lib/test-manager/dto/findAndUpdate.dto.ts", "hash": "c282840baf907b3ab30a9e389cf86b8c517ef931", "deps": ["npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/db/src/lib/test-manager/test-manager.model.ts", "hash": "ed2c331c8344d83c38b21d4605a12037d8ce9809", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger", "npm:class-validator"]}, {"file": "libs/db/src/lib/test-manager/test-manager.module.ts", "hash": "a4161403f40860085e37ca4dd763545048f646bb", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/test-manager/test-manager.service.ts", "hash": "6affc38116652248629ebb0bdaffe6143490cc4a", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/testcases/testcase.model.ts", "hash": "532ecfd64b79d233f7aae44021ba0847ed3bafa3", "deps": ["npm:sequelize-typescript", "npm:@nestjs/swagger"]}, {"file": "libs/db/src/lib/testcases/testcase.module.ts", "hash": "1aad6810ca5f730bc41c05017153f77c8b260a8f", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/testcases/testcase.service.ts", "hash": "99930747e2ec817df1b226eeb29f7e0add8ce243", "deps": ["npm:@nestjs/common", "npm:@nestjs/sequelize"]}, {"file": "libs/db/src/lib/time-duration/time-duration.model.ts", "hash": "2bc9ceca68566a37ea26f83f295ada2c9b11065d", "deps": ["npm:@nestjs/swagger", "npm:sequelize-typescript"]}, {"file": "libs/db/src/lib/utils/fillDummyAssessmentData.ts", "hash": "d97e5406e8adf27e1ec3770a89d425ce63fe70fa"}, {"file": "libs/db/tsconfig.json", "hash": "2cac7b7d3b0f76eb4cf80fece1fce29357beb1fe"}, {"file": "libs/db/tsconfig.lib.json", "hash": "030dc8c8374ad9564038694332f9d864c54b05c4"}, {"file": "libs/db/tsconfig.spec.json", "hash": "67f149c4c04b9fad28e3b59462e55ba2d1e1d09b"}]}}}, "externalNodes": {"npm:@azure/msal-node": {"type": "npm", "name": "npm:@azure/msal-node", "data": {"version": "3.6.2", "packageName": "@azure/msal-node"}}, "npm:@convergence/jwt-util": {"type": "npm", "name": "npm:@convergence/jwt-util", "data": {"version": "^0.2.0", "packageName": "@convergence/jwt-util"}}, "npm:@dropbox/sign": {"type": "npm", "name": "npm:@dropbox/sign", "data": {"version": "^1.3.0", "packageName": "@dropbox/sign"}}, "npm:@google-cloud/local-auth": {"type": "npm", "name": "npm:@google-cloud/local-auth", "data": {"version": "^2.1.0", "packageName": "@google-cloud/local-auth"}}, "npm:@google-cloud/talent": {"type": "npm", "name": "npm:@google-cloud/talent", "data": {"version": "^4.1.1", "packageName": "@google-cloud/talent"}}, "npm:@langchain/core": {"type": "npm", "name": "npm:@langchain/core", "data": {"version": "^0.3.57", "packageName": "@langchain/core"}}, "npm:@langchain/langgraph": {"type": "npm", "name": "npm:@langchain/langgraph", "data": {"version": "^0.3.0", "packageName": "@langchain/langgraph"}}, "npm:@langchain/openai": {"type": "npm", "name": "npm:@langchain/openai", "data": {"version": "^0.0.28", "packageName": "@langchain/openai"}}, "npm:@microsoft/microsoft-graph-client": {"type": "npm", "name": "npm:@microsoft/microsoft-graph-client", "data": {"version": "^3.0.4", "packageName": "@microsoft/microsoft-graph-client"}}, "npm:@nestjs/axios": {"type": "npm", "name": "npm:@nestjs/axios", "data": {"version": "^3.0.0", "packageName": "@nestjs/axios"}}, "npm:@nestjs/common": {"type": "npm", "name": "npm:@nestjs/common", "data": {"version": "^8.0.0", "packageName": "@nestjs/common"}}, "npm:@nestjs/config": {"type": "npm", "name": "npm:@nestjs/config", "data": {"version": "^1.1.6", "packageName": "@nestjs/config"}}, "npm:@nestjs/core": {"type": "npm", "name": "npm:@nestjs/core", "data": {"version": "^8.0.0", "packageName": "@nestjs/core"}}, "npm:@nestjs/microservices": {"type": "npm", "name": "npm:@nestjs/microservices", "data": {"version": "^8.2.4", "packageName": "@nestjs/microservices"}}, "npm:@nestjs/passport": {"type": "npm", "name": "npm:@nestjs/passport", "data": {"version": "^9.0.0", "packageName": "@nestjs/passport"}}, "npm:@nestjs/platform-express": {"type": "npm", "name": "npm:@nestjs/platform-express", "data": {"version": "^8.0.0", "packageName": "@nestjs/platform-express"}}, "npm:@nestjs/platform-socket.io": {"type": "npm", "name": "npm:@nestjs/platform-socket.io", "data": {"version": "^8.2.5", "packageName": "@nestjs/platform-socket.io"}}, "npm:@nestjs/sequelize": {"type": "npm", "name": "npm:@nestjs/sequelize", "data": {"version": "^8.0.0", "packageName": "@nestjs/sequelize"}}, "npm:@nestjs/swagger": {"type": "npm", "name": "npm:@nestjs/swagger", "data": {"version": "^5.2.0", "packageName": "@nestjs/swagger"}}, "npm:@nestjs/websockets": {"type": "npm", "name": "npm:@nestjs/websockets", "data": {"version": "^8.2.5", "packageName": "@nestjs/websockets"}}, "npm:@opensearch-project/opensearch": {"type": "npm", "name": "npm:@opensearch-project/opensearch", "data": {"version": "^2.5.0", "packageName": "@opensearch-project/opensearch"}}, "npm:@qdrant/qdrant-js": {"type": "npm", "name": "npm:@qdrant/qdrant-js", "data": {"version": "^1.13.0", "packageName": "@qdrant/qdrant-js"}}, "npm:@sendgrid/mail": {"type": "npm", "name": "npm:@sendgrid/mail", "data": {"version": "^7.6.0", "packageName": "@sendgrid/mail"}}, "npm:@temporalio/activity": {"type": "npm", "name": "npm:@temporalio/activity", "data": {"version": "^1.11.2", "packageName": "@temporalio/activity"}}, "npm:@temporalio/client": {"type": "npm", "name": "npm:@temporalio/client", "data": {"version": "^1.11.2", "packageName": "@temporalio/client"}}, "npm:@temporalio/common": {"type": "npm", "name": "npm:@temporalio/common", "data": {"version": "^1.11.2", "packageName": "@temporalio/common"}}, "npm:@temporalio/worker": {"type": "npm", "name": "npm:@temporalio/worker", "data": {"version": "^1.11.2", "packageName": "@temporalio/worker"}}, "npm:@temporalio/workflow": {"type": "npm", "name": "npm:@temporalio/workflow", "data": {"version": "^1.11.2", "packageName": "@temporalio/workflow"}}, "npm:@types/archiver": {"type": "npm", "name": "npm:@types/archiver", "data": {"version": "^5.3.1", "packageName": "@types/archiver"}}, "npm:@types/base-64": {"type": "npm", "name": "npm:@types/base-64", "data": {"version": "^1.0.0", "packageName": "@types/base-64"}}, "npm:@types/multer": {"type": "npm", "name": "npm:@types/multer", "data": {"version": "^1.4.7", "packageName": "@types/multer"}}, "npm:archiver": {"type": "npm", "name": "npm:archiver", "data": {"version": "^5.3.1", "packageName": "archiver"}}, "npm:aws-sdk": {"type": "npm", "name": "npm:aws-sdk", "data": {"version": "2.1059.0", "packageName": "aws-sdk"}}, "npm:base-64": {"type": "npm", "name": "npm:base-64", "data": {"version": "^1.0.0", "packageName": "base-64"}}, "npm:bcrypt": {"type": "npm", "name": "npm:bcrypt", "data": {"version": "^5.0.1", "packageName": "bcrypt"}}, "npm:class-transformer": {"type": "npm", "name": "npm:class-transformer", "data": {"version": "^0.5.1", "packageName": "class-transformer"}}, "npm:class-validator": {"type": "npm", "name": "npm:class-validator", "data": {"version": "^0.13.2", "packageName": "class-validator"}}, "npm:cross-fetch": {"type": "npm", "name": "npm:cross-fetch", "data": {"version": "^3.1.5", "packageName": "cross-fetch"}}, "npm:dotenv": {"type": "npm", "name": "npm:dotenv", "data": {"version": "^16.4.7", "packageName": "dotenv"}}, "npm:express-jwt": {"type": "npm", "name": "npm:express-jwt", "data": {"version": "^6.1.0", "packageName": "express-jwt"}}, "npm:firebase": {"type": "npm", "name": "npm:firebase", "data": {"version": "^10.12.2", "packageName": "firebase"}}, "npm:firebase-admin": {"type": "npm", "name": "npm:firebase-admin", "data": {"version": "^12.1.1", "packageName": "firebase-admin"}}, "npm:googleapis": {"type": "npm", "name": "npm:googlea<PERSON>", "data": {"version": "^107.0.0", "packageName": "googlea<PERSON>"}}, "npm:isomorphic-fetch": {"type": "npm", "name": "npm:isomorphic-fetch", "data": {"version": "^3.0.0", "packageName": "isomorphic-fetch"}}, "npm:jwks-rsa": {"type": "npm", "name": "npm:jwks-rsa", "data": {"version": "^2.0.5", "packageName": "jwks-rsa"}}, "npm:langchain": {"type": "npm", "name": "npm:langchain", "data": {"version": "^0.1.36", "packageName": "langchain"}}, "npm:langgraph": {"type": "npm", "name": "npm:langgraph", "data": {"version": "langchain-ai/langgraph", "packageName": "langgraph"}}, "npm:moment": {"type": "npm", "name": "npm:moment", "data": {"version": "^2.29.4", "packageName": "moment"}}, "npm:multer": {"type": "npm", "name": "npm:multer", "data": {"version": "1.4.4", "packageName": "multer"}}, "npm:multer-s3": {"type": "npm", "name": "npm:multer-s3", "data": {"version": "2.10.0", "packageName": "multer-s3"}}, "npm:mysql2": {"type": "npm", "name": "npm:mysql2", "data": {"version": "^2.3.3", "packageName": "mysql2"}}, "npm:nestjs-stripe": {"type": "npm", "name": "npm:nestjs-stripe", "data": {"version": "1.0.0", "packageName": "nestjs-stripe"}}, "npm:nodemailer": {"type": "npm", "name": "npm:nodemailer", "data": {"version": "^6.7.2", "packageName": "nodemailer"}}, "npm:passport": {"type": "npm", "name": "npm:passport", "data": {"version": "^0.6.0", "packageName": "passport"}}, "npm:passport-jwt": {"type": "npm", "name": "npm:passport-jwt", "data": {"version": "^4.0.0", "packageName": "passport-jwt"}}, "npm:pdf-parse": {"type": "npm", "name": "npm:pdf-parse", "data": {"version": "^1.1.1", "packageName": "pdf-parse"}}, "npm:pg": {"type": "npm", "name": "npm:pg", "data": {"version": "^8.11.5", "packageName": "pg"}}, "npm:puppeteer": {"type": "npm", "name": "npm:puppeteer", "data": {"version": "^22.6.2", "packageName": "puppeteer"}}, "npm:redis": {"type": "npm", "name": "npm:redis", "data": {"version": "^3", "packageName": "redis"}}, "npm:reflect-metadata": {"type": "npm", "name": "npm:reflect-metadata", "data": {"version": "^0.1.13", "packageName": "reflect-metadata"}}, "npm:rxjs": {"type": "npm", "name": "npm:rxjs", "data": {"version": "^7.0.0", "packageName": "rxjs"}}, "npm:sequelize": {"type": "npm", "name": "npm:sequelize", "data": {"version": "^6.25.3", "packageName": "sequelize"}}, "npm:sequelize-typescript": {"type": "npm", "name": "npm:sequelize-typescript", "data": {"version": "^2.1.5", "packageName": "sequelize-typescript"}}, "npm:sqlite3": {"type": "npm", "name": "npm:sqlite3", "data": {"version": "^5.1.1", "packageName": "sqlite3"}}, "npm:stripe": {"type": "npm", "name": "npm:stripe", "data": {"version": "^8.222.0", "packageName": "stripe"}}, "npm:swagger-ui-express": {"type": "npm", "name": "npm:swagger-ui-express", "data": {"version": "^4.3.0", "packageName": "swagger-ui-express"}}, "npm:ts-node": {"type": "npm", "name": "npm:ts-node", "data": {"version": "^10.9.2", "packageName": "ts-node"}}, "npm:tslib": {"type": "npm", "name": "npm:tslib", "data": {"version": "^2.0.0", "packageName": "tslib"}}, "npm:twilio": {"type": "npm", "name": "npm:twi<PERSON>", "data": {"version": "^3.81.0", "packageName": "twi<PERSON>"}}, "npm:undici": {"type": "npm", "name": "npm:undici", "data": {"version": "^5.5.1", "packageName": "undici"}}, "npm:uuid": {"type": "npm", "name": "npm:uuid", "data": {"version": "^9.0.0", "packageName": "uuid"}}, "npm:zod": {"type": "npm", "name": "npm:zod", "data": {"version": "^3.23.5", "packageName": "zod"}}, "npm:@macpaw/eslint-config-base": {"type": "npm", "name": "npm:@macpaw/eslint-config-base", "data": {"version": "^2.0.1", "packageName": "@macpaw/eslint-config-base"}}, "npm:@macpaw/eslint-config-typescript": {"type": "npm", "name": "npm:@macpaw/eslint-config-typescript", "data": {"version": "^2.0.1", "packageName": "@macpaw/eslint-config-typescript"}}, "npm:@microsoft/microsoft-graph-types": {"type": "npm", "name": "npm:@microsoft/microsoft-graph-types", "data": {"version": "^2.25.0", "packageName": "@microsoft/microsoft-graph-types"}}, "npm:@nestjs/schematics": {"type": "npm", "name": "npm:@nestjs/schematics", "data": {"version": "^8.0.0", "packageName": "@nestjs/schematics"}}, "npm:@nestjs/testing": {"type": "npm", "name": "npm:@nestjs/testing", "data": {"version": "^8.0.0", "packageName": "@nestjs/testing"}}, "npm:@nrwl/cli": {"type": "npm", "name": "npm:@nrwl/cli", "data": {"version": "13.4.3", "packageName": "@nrwl/cli"}}, "npm:@nrwl/eslint-plugin-nx": {"type": "npm", "name": "npm:@nrwl/eslint-plugin-nx", "data": {"version": "13.4.3", "packageName": "@nrwl/eslint-plugin-nx"}}, "npm:@nrwl/jest": {"type": "npm", "name": "npm:@nrwl/jest", "data": {"version": "13.4.3", "packageName": "@nrwl/jest"}}, "npm:@nrwl/linter": {"type": "npm", "name": "npm:@nrwl/linter", "data": {"version": "13.4.3", "packageName": "@nrwl/linter"}}, "npm:@nrwl/nest": {"type": "npm", "name": "npm:@nrwl/nest", "data": {"version": "13.4.3", "packageName": "@nrwl/nest"}}, "npm:@nrwl/node": {"type": "npm", "name": "npm:@nrwl/node", "data": {"version": "13.4.3", "packageName": "@nrwl/node"}}, "npm:@nrwl/tao": {"type": "npm", "name": "npm:@nrwl/tao", "data": {"version": "13.4.3", "packageName": "@nrwl/tao"}}, "npm:@nrwl/workspace": {"type": "npm", "name": "npm:@nrwl/workspace", "data": {"version": "13.4.3", "packageName": "@nrwl/workspace"}}, "npm:@types/jest": {"type": "npm", "name": "npm:@types/jest", "data": {"version": "27.0.2", "packageName": "@types/jest"}}, "npm:@types/node": {"type": "npm", "name": "npm:@types/node", "data": {"version": "14.14.33", "packageName": "@types/node"}}, "npm:@types/nodemailer": {"type": "npm", "name": "npm:@types/nodemailer", "data": {"version": "^6.4.4", "packageName": "@types/nodemailer"}}, "npm:@types/passport-jwt": {"type": "npm", "name": "npm:@types/passport-jwt", "data": {"version": "^3.0.6", "packageName": "@types/passport-jwt"}}, "npm:@types/sequelize": {"type": "npm", "name": "npm:@types/sequelize", "data": {"version": "^4.28.14", "packageName": "@types/sequelize"}}, "npm:@types/uuid": {"type": "npm", "name": "npm:@types/uuid", "data": {"version": "^8.3.4", "packageName": "@types/uuid"}}, "npm:@typescript-eslint/eslint-plugin": {"type": "npm", "name": "npm:@typescript-eslint/eslint-plugin", "data": {"version": "^8.18.0", "packageName": "@typescript-eslint/eslint-plugin"}}, "npm:@typescript-eslint/parser": {"type": "npm", "name": "npm:@typescript-eslint/parser", "data": {"version": "^8.18.0", "packageName": "@typescript-eslint/parser"}}, "npm:eslint": {"type": "npm", "name": "npm:eslint", "data": {"version": "8.57.1", "packageName": "eslint"}}, "npm:eslint-config-prettier": {"type": "npm", "name": "npm:eslint-config-prettier", "data": {"version": "8.1.0", "packageName": "eslint-config-prettier"}}, "npm:husky": {"type": "npm", "name": "npm:husky", "data": {"version": "^7.0.4", "packageName": "husky"}}, "npm:jest": {"type": "npm", "name": "npm:jest", "data": {"version": "30.0.4", "packageName": "jest"}}, "npm:lint-staged": {"type": "npm", "name": "npm:lint-staged", "data": {"version": "^12.3.1", "packageName": "lint-staged"}}, "npm:prettier": {"type": "npm", "name": "npm:prettier", "data": {"version": "^2.3.1", "packageName": "prettier"}}, "npm:ts-jest": {"type": "npm", "name": "npm:ts-jest", "data": {"version": "27.0.5", "packageName": "ts-jest"}}, "npm:typescript": {"type": "npm", "name": "npm:typescript", "data": {"version": "^5.6.3", "packageName": "typescript"}}}, "dependencies": {"pre-recruitment-api": [{"source": "pre-recruitment-api", "target": "npm:@nestjs/common", "type": "static"}, {"source": "pre-recruitment-api", "target": "npm:@nestjs/microservices", "type": "static"}, {"source": "pre-recruitment-api", "target": "recruitment-db", "type": "static"}, {"source": "pre-recruitment-api", "target": "integrations", "type": "static"}, {"source": "pre-recruitment-api", "target": "auth", "type": "static"}, {"source": "pre-recruitment-api", "target": "email", "type": "static"}, {"source": "pre-recruitment-api", "target": "calendars", "type": "static"}, {"source": "pre-recruitment-api", "target": "twi<PERSON>", "type": "static"}, {"source": "pre-recruitment-api", "target": "db", "type": "static"}, {"source": "pre-recruitment-api", "target": "npm:@nestjs/axios", "type": "static"}, {"source": "pre-recruitment-api", "target": "hr-analytics", "type": "static"}, {"source": "pre-recruitment-api", "target": "npm:@nestjs/swagger", "type": "static"}, {"source": "pre-recruitment-api", "target": "npm:@nestjs/passport", "type": "static"}, {"source": "pre-recruitment-api", "target": "npm:@nestjs/sequelize", "type": "static"}, {"source": "pre-recruitment-api", "target": "temporal", "type": "static"}, {"source": "pre-recruitment-api", "target": "npm:@nestjs/platform-express", "type": "static"}, {"source": "pre-recruitment-api", "target": "npm:multer", "type": "static"}, {"source": "pre-recruitment-api", "target": "npm:aws-sdk", "type": "static"}, {"source": "pre-recruitment-api", "target": "npm:multer-s3", "type": "static"}, {"source": "pre-recruitment-api", "target": "npm:@langchain/openai", "type": "static"}, {"source": "pre-recruitment-api", "target": "npm:langchain", "type": "static"}, {"source": "pre-recruitment-api", "target": "npm:@langchain/langgraph", "type": "static"}, {"source": "pre-recruitment-api", "target": "npm:@nestjs/core", "type": "static"}, {"source": "pre-recruitment-api", "target": "qdrant", "type": "static"}, {"source": "pre-recruitment-api", "target": "kinesis", "type": "static"}, {"source": "pre-recruitment-api", "target": "npm:uuid", "type": "static"}], "hr-analytics-api": [{"source": "hr-analytics-api", "target": "npm:@nestjs/common", "type": "static"}, {"source": "hr-analytics-api", "target": "npm:@nestjs/swagger", "type": "static"}, {"source": "hr-analytics-api", "target": "hr-analytics", "type": "static"}, {"source": "hr-analytics-api", "target": "npm:@nestjs/microservices", "type": "static"}, {"source": "hr-analytics-api", "target": "auth", "type": "static"}, {"source": "hr-analytics-api", "target": "npm:@nestjs/passport", "type": "static"}, {"source": "hr-analytics-api", "target": "npm:class-validator", "type": "static"}, {"source": "hr-analytics-api", "target": "npm:class-transformer", "type": "static"}, {"source": "hr-analytics-api", "target": "npm:@nestjs/core", "type": "static"}], "integrations-api": [{"source": "integrations-api", "target": "npm:@nestjs/common", "type": "static"}, {"source": "integrations-api", "target": "npm:@nestjs/microservices", "type": "static"}, {"source": "integrations-api", "target": "email", "type": "static"}, {"source": "integrations-api", "target": "npm:@nestjs/axios", "type": "static"}, {"source": "integrations-api", "target": "npm:@nestjs/swagger", "type": "static"}, {"source": "integrations-api", "target": "npm:class-validator", "type": "static"}, {"source": "integrations-api", "target": "npm:sequelize-typescript", "type": "static"}, {"source": "integrations-api", "target": "npm:@nestjs/testing", "type": "static"}, {"source": "integrations-api", "target": "npm:@google-cloud/talent", "type": "static"}, {"source": "integrations-api", "target": "npm:@nestjs/core", "type": "static"}], "assessment-api": [{"source": "assessment-api", "target": "npm:@nestjs/testing", "type": "static"}, {"source": "assessment-api", "target": "code-executor", "type": "static"}, {"source": "assessment-api", "target": "db", "type": "static"}, {"source": "assessment-api", "target": "npm:@nestjs/swagger", "type": "static"}, {"source": "assessment-api", "target": "npm:@nestjs/common", "type": "static"}, {"source": "assessment-api", "target": "auth", "type": "static"}, {"source": "assessment-api", "target": "npm:@nestjs/passport", "type": "static"}, {"source": "assessment-api", "target": "editor-manager", "type": "static"}, {"source": "assessment-api", "target": "email", "type": "static"}, {"source": "assessment-api", "target": "npm:@nestjs/microservices", "type": "static"}, {"source": "assessment-api", "target": "npm:@nestjs/axios", "type": "static"}, {"source": "assessment-api", "target": "temporal", "type": "static"}, {"source": "assessment-api", "target": "npm:@nestjs/websockets", "type": "static"}, {"source": "assessment-api", "target": "npm:class-validator", "type": "static"}, {"source": "assessment-api", "target": "npm:@nestjs/config", "type": "static"}, {"source": "assessment-api", "target": "npm:rxjs", "type": "static"}, {"source": "assessment-api", "target": "npm:@nestjs/core", "type": "static"}, {"source": "assessment-api", "target": "qdrant", "type": "static"}, {"source": "assessment-api", "target": "kinesis", "type": "static"}], "editor-manager": [{"source": "editor-manager", "target": "npm:@nestjs/common", "type": "static"}, {"source": "editor-manager", "target": "npm:@nestjs/testing", "type": "static"}], "recruitment-db": [{"source": "recruitment-db", "target": "npm:sequelize-typescript", "type": "static"}, {"source": "recruitment-db", "target": "npm:@nestjs/swagger", "type": "static"}, {"source": "recruitment-db", "target": "npm:@nestjs/common", "type": "static"}, {"source": "recruitment-db", "target": "npm:@nestjs/sequelize", "type": "static"}, {"source": "recruitment-db", "target": "npm:class-validator", "type": "static"}, {"source": "recruitment-db", "target": "pre-recruitment-api", "type": "static"}, {"source": "recruitment-db", "target": "npm:@nestjs/axios", "type": "static"}, {"source": "recruitment-db", "target": "email", "type": "static"}, {"source": "recruitment-db", "target": "npm:sequelize", "type": "static"}, {"source": "recruitment-db", "target": "temporal", "type": "static"}, {"source": "recruitment-db", "target": "auth", "type": "static"}, {"source": "recruitment-db", "target": "integrations", "type": "static"}, {"source": "recruitment-db", "target": "npm:uuid", "type": "static"}, {"source": "recruitment-db", "target": "npm:bcrypt", "type": "static"}, {"source": "recruitment-db", "target": "npm:googlea<PERSON>", "type": "static"}, {"source": "recruitment-db", "target": "npm:@dropbox/sign", "type": "static"}, {"source": "recruitment-db", "target": "npm:@nestjs/platform-express", "type": "static"}, {"source": "recruitment-db", "target": "npm:@microsoft/microsoft-graph-client", "type": "static"}, {"source": "recruitment-db", "target": "npm:cross-fetch", "type": "static"}, {"source": "recruitment-db", "target": "npm:isomorphic-fetch", "type": "static"}, {"source": "recruitment-db", "target": "npm:zod", "type": "static"}, {"source": "recruitment-db", "target": "npm:@langchain/openai", "type": "static"}, {"source": "recruitment-db", "target": "npm:langchain", "type": "static"}, {"source": "recruitment-db", "target": "npm:@qdrant/qdrant-js", "type": "static"}, {"source": "recruitment-db", "target": "npm:aws-sdk", "type": "static"}, {"source": "recruitment-db", "target": "qdrant", "type": "static"}, {"source": "recruitment-db", "target": "npm:firebase-admin", "type": "static"}, {"source": "recruitment-db", "target": "npm:puppeteer", "type": "static"}, {"source": "recruitment-db", "target": "npm:nestjs-stripe", "type": "static"}, {"source": "recruitment-db", "target": "npm:stripe", "type": "static"}, {"source": "recruitment-db", "target": "npm:@nestjs/config", "type": "static"}, {"source": "recruitment-db", "target": "npm:@temporalio/common", "type": "static"}, {"source": "recruitment-db", "target": "npm:@langchain/core", "type": "static"}], "code-executor": [{"source": "code-executor", "target": "npm:@nestjs/axios", "type": "static"}, {"source": "code-executor", "target": "npm:@nestjs/common", "type": "static"}, {"source": "code-executor", "target": "npm:@nestjs/config", "type": "static"}, {"source": "code-executor", "target": "npm:@nestjs/testing", "type": "static"}, {"source": "code-executor", "target": "npm:undici", "type": "static"}, {"source": "code-executor", "target": "npm:rxjs", "type": "static"}], "hr-analytics": [{"source": "hr-analytics", "target": "npm:@nestjs/common", "type": "static"}, {"source": "hr-analytics", "target": "npm:moment", "type": "static"}, {"source": "hr-analytics", "target": "npm:@opensearch-project/opensearch", "type": "static"}, {"source": "hr-analytics", "target": "npm:@nestjs/swagger", "type": "static"}, {"source": "hr-analytics", "target": "npm:class-validator", "type": "static"}, {"source": "hr-analytics", "target": "npm:rxjs", "type": "static"}], "integrations": [{"source": "integrations", "target": "npm:@nestjs/common", "type": "static"}, {"source": "integrations", "target": "npm:@google-cloud/talent", "type": "static"}, {"source": "integrations", "target": "npm:langchain", "type": "static"}, {"source": "integrations", "target": "npm:zod", "type": "static"}, {"source": "integrations", "target": "npm:@langchain/openai", "type": "static"}], "calendars": [{"source": "calendars", "target": "recruitment-db", "type": "static"}, {"source": "calendars", "target": "npm:@nestjs/common", "type": "static"}, {"source": "calendars", "target": "db", "type": "static"}, {"source": "calendars", "target": "npm:@nestjs/axios", "type": "static"}, {"source": "calendars", "target": "npm:@nestjs/testing", "type": "static"}, {"source": "calendars", "target": "npm:@nestjs/swagger", "type": "static"}, {"source": "calendars", "target": "npm:class-validator", "type": "static"}, {"source": "calendars", "target": "npm:googlea<PERSON>", "type": "static"}, {"source": "calendars", "target": "npm:uuid", "type": "static"}, {"source": "calendars", "target": "temporal", "type": "static"}, {"source": "calendars", "target": "npm:@microsoft/microsoft-graph-client", "type": "static"}, {"source": "calendars", "target": "npm:@azure/msal-node", "type": "static"}, {"source": "calendars", "target": "npm:cross-fetch", "type": "static"}, {"source": "calendars", "target": "npm:isomorphic-fetch", "type": "static"}], "temporal": [{"source": "temporal", "target": "npm:@nestjs/testing", "type": "static"}, {"source": "temporal", "target": "npm:@nestjs/common", "type": "static"}, {"source": "temporal", "target": "email", "type": "static"}, {"source": "temporal", "target": "npm:@nestjs/axios", "type": "static"}, {"source": "temporal", "target": "npm:@nestjs/swagger", "type": "static"}, {"source": "temporal", "target": "npm:class-validator", "type": "static"}, {"source": "temporal", "target": "npm:sequelize-typescript", "type": "static"}, {"source": "temporal", "target": "npm:@temporalio/worker", "type": "static"}, {"source": "temporal", "target": "npm:@temporalio/workflow", "type": "static"}, {"source": "temporal", "target": "npm:@temporalio/client", "type": "static"}, {"source": "temporal", "target": "npm:@nestjs/core", "type": "static"}], "kinesis": [{"source": "kinesis", "target": "npm:@nestjs/common", "type": "static"}, {"source": "kinesis", "target": "npm:aws-sdk", "type": "static"}], "qdrant": [{"source": "qdrant", "target": "npm:@nestjs/common", "type": "static"}, {"source": "qdrant", "target": "npm:aws-sdk", "type": "static"}, {"source": "qdrant", "target": "npm:@qdrant/qdrant-js", "type": "static"}, {"source": "qdrant", "target": "integrations", "type": "static"}], "twilio": [{"source": "twi<PERSON>", "target": "npm:@nestjs/common", "type": "static"}, {"source": "twi<PERSON>", "target": "npm:twi<PERSON>", "type": "static"}, {"source": "twi<PERSON>", "target": "npm:@nestjs/axios", "type": "static"}, {"source": "twi<PERSON>", "target": "db", "type": "static"}, {"source": "twi<PERSON>", "target": "recruitment-db", "type": "static"}, {"source": "twi<PERSON>", "target": "npm:@nestjs/testing", "type": "static"}, {"source": "twi<PERSON>", "target": "npm:uuid", "type": "static"}, {"source": "twi<PERSON>", "target": "temporal", "type": "static"}], "email": [{"source": "email", "target": "npm:@nestjs/common", "type": "static"}, {"source": "email", "target": "npm:@nestjs/config", "type": "static"}, {"source": "email", "target": "npm:@nestjs/axios", "type": "static"}, {"source": "email", "target": "npm:@nestjs/testing", "type": "static"}, {"source": "email", "target": "npm:@sendgrid/mail", "type": "static"}, {"source": "email", "target": "npm:nodemailer", "type": "static"}], "auth": [{"source": "auth", "target": "npm:@nestjs/testing", "type": "static"}, {"source": "auth", "target": "npm:@nestjs/common", "type": "static"}, {"source": "auth", "target": "npm:@nestjs/passport", "type": "static"}, {"source": "auth", "target": "npm:cross-fetch", "type": "static"}, {"source": "auth", "target": "npm:passport-jwt", "type": "static"}, {"source": "auth", "target": "npm:jwks-rsa", "type": "static"}, {"source": "auth", "target": "npm:dotenv", "type": "static"}, {"source": "auth", "target": "npm:@nestjs/swagger", "type": "static"}, {"source": "auth", "target": "npm:class-validator", "type": "static"}, {"source": "auth", "target": "npm:rxjs", "type": "static"}, {"source": "auth", "target": "npm:@nestjs/core", "type": "static"}], "db": [{"source": "db", "target": "npm:sequelize-typescript", "type": "static"}, {"source": "db", "target": "npm:@nestjs/swagger", "type": "static"}, {"source": "db", "target": "npm:@nestjs/common", "type": "static"}, {"source": "db", "target": "npm:@nestjs/config", "type": "static"}, {"source": "db", "target": "npm:@nestjs/sequelize", "type": "static"}, {"source": "db", "target": "npm:@langchain/openai", "type": "static"}, {"source": "db", "target": "npm:@langchain/core", "type": "static"}, {"source": "db", "target": "npm:sequelize", "type": "static"}, {"source": "db", "target": "npm:class-validator", "type": "static"}, {"source": "db", "target": "npm:moment", "type": "static"}, {"source": "db", "target": "email", "type": "static"}, {"source": "db", "target": "npm:@nestjs/testing", "type": "static"}, {"source": "db", "target": "qdrant", "type": "static"}, {"source": "db", "target": "npm:@convergence/jwt-util", "type": "static"}, {"source": "db", "target": "npm:uuid", "type": "static"}, {"source": "db", "target": "npm:undici", "type": "static"}]}}