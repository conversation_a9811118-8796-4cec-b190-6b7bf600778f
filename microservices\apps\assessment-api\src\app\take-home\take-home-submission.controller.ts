import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthUser, Permissions, PermissionsGuard } from '@microservices/auth';
import {
  TakeHomeSubmissionService,
  CreateSubmissionDto,
  UpdateSubmissionDto,
} from '@microservices/db';
import { CodeExecutorService, OpenAIExecutorService } from '@microservices/code-executor';

@Controller('take-home-submissions')
@ApiBearerAuth()
export class TakeHomeSubmissionController {
  constructor(
    private readonly takeHomeSubmissionService: TakeHomeSubmissionService,
    private readonly codeExecutorService: CodeExecutorService,
    private readonly openAIExecutorService: OpenAIExecutorService,
  ) {}

  @ApiOperation({ summary: 'Submit take-home assessment with code execution' })
  @ApiResponse({ status: 201, description: 'Assessment submitted and evaluated successfully' })
  @ApiResponse({ status: 409, description: 'Submission already exists' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post('submit')
  @Permissions('candidate')
  async submitAssessment(
    @Body() submitDto: {
      jobId: number;
      assessmentId: number;
      assignmentId?: number;
      duration?: string;
      questionSubmissions: Array<{
        questionId: number;
        code: string;
        language: string;
        languageId: number;
      }>;
    },
    @AuthUser() user: any,
  ) {
    console.log('=== TAKE-HOME SUBMISSION CONTROLLER ===');
    console.log('Received submission request');
    console.log('User from token:', user);
    console.log('Submission DTO:', JSON.stringify(submitDto, null, 2));

    const candidateId = user['https://urecruits.com/userId'];
    console.log('Candidate ID:', candidateId);

    // Process each question submission and execute test cases
    const processedSubmissions = [];
    let totalScore = 0;
    let totalTests = 0;
    let passedTests = 0;

    for (const questionSubmission of submitDto.questionSubmissions) {
      try {
        // Execute code against test cases using OpenAI executor
        const executionResult = await this.openAIExecutorService.analyzeCode({
          language_id: questionSubmission.languageId,
          source_code: questionSubmission.code,
          question: `Question ID: ${questionSubmission.questionId}`,
        });

        // Calculate score for this question
        const questionScore = this.calculateQuestionScore(executionResult);
        totalScore += questionScore;

        if (executionResult.test_cases) {
          totalTests += executionResult.test_cases.length;
          passedTests += executionResult.test_cases.filter(tc => tc.status === 'passed').length;
        }

        processedSubmissions.push({
          questionId: questionSubmission.questionId,
          code: questionSubmission.code,
          language: questionSubmission.language,
          languageId: questionSubmission.languageId,
          executionResult,
          testResults: executionResult.test_cases || [],
          score: questionScore,
        });
      } catch (error) {
        // Handle execution errors
        processedSubmissions.push({
          questionId: questionSubmission.questionId,
          code: questionSubmission.code,
          language: questionSubmission.language,
          languageId: questionSubmission.languageId,
          executionResult: null,
          testResults: [],
          score: 0,
          error: error.message,
        });
      }
    }

    // Create test case summary
    const testCaseSummary = {
      totalTests,
      passed: passedTests,
      failed: totalTests - passedTests,
      passRate: totalTests > 0 ? (passedTests / totalTests) * 100 : 0,
      details: processedSubmissions.map(sub => ({
        questionId: sub.questionId,
        testsPassed: sub.testResults.filter(tr => tr.status === 'passed').length,
        testsTotal: sub.testResults.length,
        score: sub.score,
      })),
    };

    // Create submission record
    console.log('Creating submission record...');
    const createSubmissionDto: CreateSubmissionDto = {
      jobId: submitDto.jobId,
      candidateId,
      assessmentId: submitDto.assessmentId,
      assignmentId: submitDto.assignmentId,
      duration: submitDto.duration,
      language: submitDto.questionSubmissions[0]?.language,
      questionSubmissions: processedSubmissions,
      testCaseSummary,
      submittedAt: new Date(),
    };

    console.log('Submission data to save:', JSON.stringify(createSubmissionDto, null, 2));

    const submission = await this.takeHomeSubmissionService.create(createSubmissionDto);

    console.log('Submission created successfully:', submission);

    return {
      message: 'Assessment submitted and evaluated successfully',
      data: {
        submission,
        summary: {
          totalScore,
          totalTests,
          passedTests,
          passRate: testCaseSummary.passRate,
        },
      },
      status: HttpStatus.CREATED,
    };
  }

  @ApiOperation({ summary: 'Create a new take-home submission' })
  @ApiResponse({ status: 201, description: 'Submission created successfully' })
  @ApiResponse({ status: 409, description: 'Submission already exists' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post()
  @Permissions('candidate')
  async create(
    @Body() createSubmissionDto: CreateSubmissionDto,
    @AuthUser() user: any,
  ) {
    // Set candidateId from authenticated user
    createSubmissionDto.candidateId = user['https://urecruits.com/userId'];

    const submission = await this.takeHomeSubmissionService.create(createSubmissionDto);

    return {
      message: 'Submission created successfully',
      data: submission,
      status: HttpStatus.CREATED,
    };
  }

  @ApiOperation({ summary: 'Get submission by candidate and job' })
  @ApiResponse({ status: 200, description: 'Submission found' })
  @ApiResponse({ status: 404, description: 'Submission not found' })
  @Get('candidate/:candidateId/job/:jobId')
  async findByCandidateAndJob(
    @Param('candidateId') candidateId: number,
    @Param('jobId') jobId: number,
  ) {
    return this.takeHomeSubmissionService.findByCandidateAndJob(candidateId, jobId);
  }

  @ApiOperation({ summary: 'Get submissions for review (recruiter)' })
  @ApiResponse({ status: 200, description: 'Submissions retrieved successfully' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('review')
  @Permissions('assessment:view')
  async getSubmissionsForReview(
    @Query() query: {
      limit?: string;
      offset?: string;
      search?: string;
      jobId?: string;
      assessmentId?: string;
    },
    @AuthUser() user: any,
  ) {
    const companyId = user['https://urecruits.com/companyId'];
    
    const filters = {
      limit: query.limit ? parseInt(query.limit) : 10,
      offset: query.offset ? parseInt(query.offset) : 0,
      search: query.search,
      jobId: query.jobId ? parseInt(query.jobId) : undefined,
      assessmentId: query.assessmentId ? parseInt(query.assessmentId) : undefined,
    };

    const result = await this.takeHomeSubmissionService.findForReview(
      companyId,
      filters,
    );

    return {
      data: result.rows,
      total: result.count,
      limit: filters.limit,
      offset: filters.offset,
    };
  }

  @ApiOperation({ summary: 'Get submission by ID' })
  @ApiResponse({ status: 200, description: 'Submission retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Submission not found' })
  @UseGuards(AuthGuard('jwt'))
  @Get(':id')
  async findOne(@Param('id') id: string, @AuthUser() user: any) {
    console.log('=== GET SUBMISSION BY ID ===');
    console.log('Requested ID:', id);
    console.log('User:', user);
    console.log('User permissions:', user.permissions);

    // Check permissions: either recruiter with assessment:view OR candidate
    const hasAssessmentView = user.permissions?.includes('assessment:view');
    const isCandidate = !user['https://urecruits.com/companyId'];
    const hasValidPermission = hasAssessmentView || isCandidate;

    console.log('Has assessment:view permission:', hasAssessmentView);
    console.log('Is candidate:', isCandidate);
    console.log('Has valid permission:', hasValidPermission);

    if (!hasValidPermission) {
      console.log('User lacks required permissions');
      throw new HttpException('Forbidden', HttpStatus.FORBIDDEN);
    }

    try {
      const submission = await this.takeHomeSubmissionService.findById(+id);
      console.log('Found submission:', submission);

      // Additional check: candidates can only view their own submissions
      if (isCandidate && submission.candidateId !== user['https://urecruits.com/userId']) {
        console.log('Candidate trying to access another candidate\'s submission');
        throw new HttpException('Forbidden', HttpStatus.FORBIDDEN);
      }

      return {
        data: submission,
      };
    } catch (error) {
      console.error('Error fetching submission:', error);
      throw error;
    }
  }

  @ApiOperation({ summary: 'Get submissions by job and candidate' })
  @ApiResponse({ status: 200, description: 'Submissions retrieved successfully' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('job/:jobId/candidate/:candidateId')
  @Permissions('assessment:view')
  async findByJobAndCandidate(
    @Param('jobId') jobId: string,
    @Param('candidateId') candidateId: string,
  ) {
    const submissions = await this.takeHomeSubmissionService.findByJobAndCandidate(
      +jobId,
      +candidateId,
    );

    return {
      data: submissions,
    };
  }

  @ApiOperation({ summary: 'Get submissions by assessment' })
  @ApiResponse({ status: 200, description: 'Submissions retrieved successfully' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('assessment/:assessmentId')
  @Permissions('assessment:view')
  async findByAssessment(@Param('assessmentId') assessmentId: string) {
    const submissions = await this.takeHomeSubmissionService.findByAssessment(
      +assessmentId,
    );

    return {
      data: submissions,
    };
  }

  @ApiOperation({ summary: 'Update submission (add feedback/score)' })
  @ApiResponse({ status: 200, description: 'Submission updated successfully' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Patch(':id')
  @Permissions('assessment:edit')
  async update(
    @Param('id') id: string,
    @Body() updateSubmissionDto: UpdateSubmissionDto,
  ) {
    const submission = await this.takeHomeSubmissionService.update(
      +id,
      updateSubmissionDto,
    );

    return {
      message: 'Submission updated successfully',
      data: submission,
    };
  }

  @ApiOperation({ summary: 'Delete submission' })
  @ApiResponse({ status: 200, description: 'Submission deleted successfully' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Delete(':id')
  @Permissions('assessment:edit')
  async remove(@Param('id') id: string) {
    await this.takeHomeSubmissionService.delete(+id);

    return {
      message: 'Submission deleted successfully',
    };
  }

  @ApiOperation({ summary: 'Get submission statistics for assessment' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('assessment/:assessmentId/stats')
  @Permissions('assessment:view')
  async getSubmissionStats(@Param('assessmentId') assessmentId: string) {
    const stats = await this.takeHomeSubmissionService.getSubmissionStats(
      +assessmentId,
    );

    return {
      data: stats,
    };
  }

  @ApiOperation({ summary: 'Get candidate submissions' })
  @ApiResponse({ status: 200, description: 'Candidate submissions retrieved' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('candidate/my-submissions')
  @Permissions('candidate')
  async getMyCandidateSubmissions(
    @Query() query: { jobId?: string },
    @AuthUser() user: any,
  ) {
    const candidateId = user['https://urecruits.com/userId'];
    const jobId = query.jobId ? parseInt(query.jobId) : undefined;

    let submissions;
    if (jobId) {
      submissions = await this.takeHomeSubmissionService.findByJobAndCandidate(
        jobId,
        candidateId,
      );
    } else {
      // Get all submissions for this candidate across all jobs
      submissions = await this.takeHomeSubmissionService.findByJobAndCandidate(
        0, // This would need to be modified in service to handle all jobs
        candidateId,
      );
    }

    return {
      data: submissions,
    };
  }

  private calculateQuestionScore(executionResult: any): number {
    if (!executionResult || !executionResult.test_cases) {
      return 0;
    }

    const totalTests = executionResult.test_cases.length;
    if (totalTests === 0) {
      return 0;
    }

    const passedTests = executionResult.test_cases.filter((tc: any) => tc.status === 'passed').length;

    // Base score calculation: percentage of tests passed
    let score = (passedTests / totalTests) * 100;

    // Bonus for compilation success and no errors
    if (executionResult.status?.id === 3) { // Accepted status
      score = Math.min(score + 10, 100); // Bonus points for clean execution
    }

    // Penalty for compilation errors
    if (executionResult.stderr || executionResult.compile_output) {
      score = Math.max(score - 10, 0); // Penalty for errors
    }

    return Math.round(score);
  }

  @ApiOperation({ summary: 'Get question details by question ID' })
  @ApiResponse({ status: 200, description: 'Question details retrieved successfully' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('question/:questionId')
  @Permissions('assessment:view')
  async getQuestionDetails(@Param('questionId') questionId: string) {
    const question = await this.takeHomeSubmissionService.getQuestionById(+questionId);
    return {
      data: question,
    };
  }
}
