import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import fetchData, { patchData } from '../../hook/http';
import { getEnv } from '@urecruits/api';

interface TakeHomeSubmission {
  id: number;
  candidateId: number;
  jobId: number;
  assessmentId: number;
  duration?: string;
  language?: string;
  totalScore: number;
  questionSubmissions: Array<{
    questionId: number;
    code: string;
    language: string;
    languageId: number;
    executionResult?: any;
    testResults: Array<{
      input: string;
      expectedOutput: string;
      actualOutput?: string;
      status: 'passed' | 'failed';
    }>;
    score: number;
  }>;
  testCaseSummary: {
    totalTests: number;
    passed: number;
    failed: number;
    passRate: number;
    details: Array<{
      questionId: number;
      testsPassed: number;
      testsTotal: number;
      score: number;
    }>;
  };
  submittedAt: string;
  feedback?: string;
  assessment?: {
    id: number;
    name: string;
    description: string;
    questions: Array<{
      id: number;
      name: string;
      description: string;
      candidateInstruction: string;
      outputDescription: string;
      starterCode?: string;
      languageId?: number;
    }>;
  };
}

const TakeHomeSubmissionDetail: React.FC = () => {
  const { submissionId } = useParams<{ submissionId: string }>();
  const navigate = useNavigate();
  const [submission, setSubmission] = useState<TakeHomeSubmission | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [feedback, setFeedback] = useState('');

  // Add CSS animations
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
      }

      .submission-card {
        animation: fadeIn 0.6s ease-out;
      }

      .overview-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
      }

      .back-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(9, 156, 115, 0.4) !important;
      }

      .save-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(9, 156, 115, 0.4) !important;
      }

      .textarea:focus {
        border-color: #099C73 !important;
        outline: none !important;
        box-shadow: 0 0 0 3px rgba(9, 156, 115, 0.1) !important;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  useEffect(() => {
    if (submissionId) {
      fetchSubmission();
    }
  }, [submissionId]);

  const fetchSubmission = async () => {
    try {
      setLoading(true);
      const response = await fetchData(`${getEnv().API_ASSESSMENT}/api/take-home-submissions/${submissionId}`);
      setSubmission(response.data);
      setFeedback(response.data.feedback || '');
    } catch (err) {
      setError('Failed to load submission details');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveFeedback = async () => {
    try {
      await patchData(`${getEnv().API_ASSESSMENT}/api/take-home-submissions/${submissionId}`, { feedback });
      alert('Feedback saved successfully!');
    } catch (err) {
      alert('Failed to save feedback');
      console.error(err);
    }
  };

  const styles = {
    submissionDetail: {
      minHeight: '100vh',
      background: 'linear-gradient(125.2deg, #F5FCFF 8.04%, #ACD8D1 127.26%)',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
    },
    container: {
      maxWidth: '1400px',
      margin: '0 auto',
      padding: '24px',
    },
    header: {
      background: '#ffffff',
      borderRadius: '4px',
      padding: '24px 32px',
      marginBottom: '24px',
      boxShadow: '0px 4px 20px 5px rgba(153, 158, 165, 0.1)',
      border: '1px solid #DFE2E6',
    },
    headerContent: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      flexWrap: 'wrap' as const,
      gap: '16px'
    },
    headerLeft: {
      display: 'flex',
      alignItems: 'center',
      gap: '16px'
    },
    backButton: {
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      background: 'linear-gradient(125.2deg, #099C73 8.04%, #015462 127.26%)',
      color: 'white',
      border: 'none',
      padding: '12px 20px',
      borderRadius: '4px',
      cursor: 'pointer',
      fontSize: '14px',
      fontWeight: '600',
      transition: 'all 0.3s ease',
      boxShadow: '0px 4px 20px 5px rgba(153, 158, 165, 0.1)',
    },
    title: {
      fontSize: '28px',
      fontWeight: '700',
      color: '#1a202c',
      margin: 0,
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
      backgroundClip: 'text'
    },
    statusBadge: {
      display: 'inline-flex',
      alignItems: 'center',
      gap: '6px',
      padding: '8px 16px',
      borderRadius: '20px',
      fontSize: '14px',
      fontWeight: '600',
      background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      color: 'white',
      boxShadow: '0 4px 12px rgba(79, 172, 254, 0.3)'
    },
    overviewGrid: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
      gap: '24px',
      marginBottom: '32px'
    },
    overviewCard: {
      background: '#ffffff',
      borderRadius: '4px',
      padding: '24px',
      boxShadow: '0px 4px 20px 5px rgba(153, 158, 165, 0.1)',
      border: '1px solid #DFE2E6',
      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
      position: 'relative' as const,
      overflow: 'hidden'
    },
    cardHeader: {
      display: 'flex',
      alignItems: 'center',
      gap: '12px',
      marginBottom: '16px'
    },
    cardIcon: {
      width: '40px',
      height: '40px',
      borderRadius: '12px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: '18px',
      color: 'white',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    },
    cardTitle: {
      fontSize: '18px',
      fontWeight: '600',
      color: '#2d3748',
      margin: 0
    },
    scoreDisplay: {
      fontSize: '36px',
      fontWeight: '700',
      background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
      backgroundClip: 'text',
      marginBottom: '8px'
    },
    progressBar: {
      width: '100%',
      height: '8px',
      background: '#DFE2E6',
      borderRadius: '4px',
      overflow: 'hidden',
      marginTop: '12px'
    },
    progressFill: {
      height: '100%',
      background: 'linear-gradient(125.2deg, #099C73 8.04%, #015462 127.26%)',
      borderRadius: '4px',
      transition: 'width 0.3s ease'
    },
    section: {
      background: '#ffffff',
      borderRadius: '4px',
      marginBottom: '24px',
      boxShadow: '0px 4px 20px 5px rgba(153, 158, 165, 0.1)',
      border: '1px solid #DFE2E6',
      overflow: 'hidden'
    },
    sectionHeader: {
      background: 'linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%)',
      padding: '20px 24px',
      borderBottom: '1px solid #e2e8f0',
      display: 'flex',
      alignItems: 'center',
      gap: '12px'
    },
    sectionTitle: {
      fontSize: '20px',
      fontWeight: '600',
      color: '#2d3748',
      margin: 0
    },
    sectionContent: {
      padding: '24px'
    },
    codeBlock: {
      background: '#2A2C33',
      border: '1px solid #464E57',
      borderRadius: '4px',
      padding: '20px',
      fontFamily: 'Monaco, Consolas, "SF Mono", "Cascadia Code", "Roboto Mono", Courier, monospace',
      fontSize: '14px',
      lineHeight: '1.6',
      overflow: 'auto',
      color: '#ffffff',
      boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.1)',
      position: 'relative' as const,
      maxHeight: '400px',
      whiteSpace: 'pre-wrap' as const,
      wordBreak: 'break-word' as const
    },
    codeHeader: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: '12px',
      paddingBottom: '8px',
      borderBottom: '1px solid #e2e8f0'
    },
    questionCard: {
      background: '#F8F9FB',
      border: '1px solid #DFE2E6',
      borderRadius: '4px',
      marginBottom: '20px',
      overflow: 'hidden'
    },
    questionHeader: {
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white',
      padding: '16px 20px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between'
    },
    questionTitle: {
      fontSize: '16px',
      fontWeight: '600',
      margin: 0
    },
    questionScore: {
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      fontSize: '14px',
      fontWeight: '600'
    },
    testResult: {
      padding: '16px',
      margin: '8px 0',
      borderRadius: '12px',
      fontSize: '14px',
      border: '1px solid',
      transition: 'all 0.3s ease'
    },
    testPassed: {
      background: '#E8F5F0',
      color: '#099C73',
      borderColor: '#ACD8D1',
      boxShadow: '0px 4px 20px 5px rgba(153, 158, 165, 0.1)'
    },
    testFailed: {
      background: '#FDF2F2',
      color: '#E53E3E',
      borderColor: '#FEB2B2',
      boxShadow: '0px 4px 20px 5px rgba(153, 158, 165, 0.1)'
    },
    testHeader: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: '12px'
    },
    testStatus: {
      display: 'flex',
      alignItems: 'center',
      gap: '6px',
      fontWeight: '600'
    },
    testDetails: {
      display: 'grid',
      gap: '12px'
    },
    testDetailItem: {
      background: 'rgba(255, 255, 255, 0.5)',
      padding: '12px',
      borderRadius: '8px',
      border: '1px solid rgba(255, 255, 255, 0.3)'
    },
    textarea: {
      width: '100%',
      minHeight: '120px',
      padding: '16px',
      border: '2px solid #DFE2E6',
      borderRadius: '4px',
      fontSize: '14px',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      resize: 'vertical' as const,
      transition: 'border-color 0.3s ease',
      background: '#F8F9FB'
    },
    saveButton: {
      background: 'linear-gradient(125.2deg, #099C73 8.04%, #015462 127.26%)',
      color: 'white',
      border: 'none',
      padding: '12px 24px',
      borderRadius: '4px',
      cursor: 'pointer',
      fontSize: '14px',
      fontWeight: '600',
      marginTop: '16px',
      transition: 'all 0.3s ease',
      boxShadow: '0px 4px 20px 5px rgba(153, 158, 165, 0.1)'
    },
    loadingContainer: {
      display: 'flex',
      flexDirection: 'column' as const,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '400px',
      gap: '16px'
    },
    loadingSpinner: {
      width: '40px',
      height: '40px',
      border: '4px solid #e2e8f0',
      borderTop: '4px solid #667eea',
      borderRadius: '50%',
      animation: 'spin 1s linear infinite'
    },
    errorContainer: {
      display: 'flex',
      flexDirection: 'column' as const,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '400px',
      gap: '16px',
      textAlign: 'center' as const
    },
    errorMessage: {
      fontSize: '18px',
      color: '#e53e3e',
      fontWeight: '600'
    }
  };

  if (loading) {
    return (
      <div style={styles.submissionDetail}>
        <div style={styles.container}>
          <div style={styles.loadingContainer}>
            <div style={styles.loadingSpinner}></div>
            <h3 style={{color: '#4a5568', margin: 0}}>Loading submission details...</h3>
            <p style={{color: '#718096', margin: 0}}>Please wait while we fetch the submission data</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !submission) {
    return (
      <div style={styles.submissionDetail}>
        <div style={styles.container}>
          <div style={styles.errorContainer}>
            <div style={{fontSize: '48px', marginBottom: '16px'}}>⚠️</div>
            <div style={styles.errorMessage}>{error || 'Submission not found'}</div>
            <p style={{color: '#718096', margin: '8px 0 24px 0'}}>
              The submission you're looking for doesn't exist or you don't have permission to view it.
            </p>
            <button
              onClick={() => navigate('/recruitment/review-score')}
              style={styles.backButton}
              className="back-button"
            >
              ← Back to Review & Score
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={styles.submissionDetail}>
      <div style={styles.container}>
        {/* Header */}
        <div style={styles.header}>
          <div style={styles.headerContent}>
            <div style={styles.headerLeft}>
              <button
                onClick={() => navigate('/recruitment/review-score')}
                style={styles.backButton}
                className="back-button"
              >
                ← Back
              </button>
              <h1 style={styles.title}>Take-Home Assessment Review</h1>
            </div>
            <div style={styles.statusBadge}>
              ✅ Submitted
            </div>
          </div>
        </div>

        {/* Overview Cards */}
        <div style={styles.overviewGrid}>
          <div style={styles.overviewCard} className="overview-card submission-card">
            <div style={styles.cardHeader}>
              <div style={styles.cardIcon}>📊</div>
              <h3 style={styles.cardTitle}>Overall Score</h3>
            </div>
            <div style={styles.scoreDisplay}>{submission.totalScore}%</div>
            <div style={styles.progressBar}>
              <div
                style={{
                  ...styles.progressFill,
                  width: `${submission.totalScore}%`
                }}
              ></div>
            </div>
          </div>

          <div style={styles.overviewCard} className="overview-card submission-card">
            <div style={styles.cardHeader}>
              <div style={styles.cardIcon}>🧪</div>
              <h3 style={styles.cardTitle}>Test Results</h3>
            </div>
            <div style={{display: 'flex', alignItems: 'center', gap: '16px', marginBottom: '12px'}}>
              <div>
                <div style={{fontSize: '24px', fontWeight: '700', color: '#48bb78'}}>
                  {submission.testCaseSummary.passed}
                </div>
                <div style={{fontSize: '12px', color: '#718096'}}>Passed</div>
              </div>
              <div style={{fontSize: '20px', color: '#a0aec0'}}>/</div>
              <div>
                <div style={{fontSize: '24px', fontWeight: '700', color: '#4a5568'}}>
                  {submission.testCaseSummary.totalTests}
                </div>
                <div style={{fontSize: '12px', color: '#718096'}}>Total</div>
              </div>
            </div>
            <div style={{fontSize: '14px', color: '#4a5568'}}>
              Pass Rate: <strong>{submission.testCaseSummary.passRate.toFixed(1)}%</strong>
            </div>
          </div>

          <div style={styles.overviewCard} className="overview-card submission-card">
            <div style={styles.cardHeader}>
              <div style={styles.cardIcon}>👤</div>
              <h3 style={styles.cardTitle}>Candidate Info</h3>
            </div>
            <div style={{marginBottom: '12px'}}>
              <div style={{fontSize: '14px', color: '#718096', marginBottom: '4px'}}>Candidate ID</div>
              <div style={{fontSize: '16px', fontWeight: '600', color: '#2d3748'}}>{submission.candidateId}</div>
            </div>
            <div style={{marginBottom: '12px'}}>
              <div style={{fontSize: '14px', color: '#718096', marginBottom: '4px'}}>Duration</div>
              <div style={{fontSize: '16px', fontWeight: '600', color: '#2d3748'}}>{submission.duration || 'N/A'}</div>
            </div>
          </div>

          <div style={styles.overviewCard} className="overview-card submission-card">
            <div style={styles.cardHeader}>
              <div style={styles.cardIcon}>📅</div>
              <h3 style={styles.cardTitle}>Submission Details</h3>
            </div>
            <div style={{marginBottom: '12px'}}>
              <div style={{fontSize: '14px', color: '#718096', marginBottom: '4px'}}>Submitted At</div>
              <div style={{fontSize: '16px', fontWeight: '600', color: '#2d3748'}}>
                {new Date(submission.submittedAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </div>
            </div>
            <div>
              <div style={{fontSize: '14px', color: '#718096', marginBottom: '4px'}}>Languages Used</div>
              <div style={{fontSize: '16px', fontWeight: '600', color: '#2d3748'}}>
                {(() => {
                  const languages = Array.from(new Set(submission.questionSubmissions.map(qs => qs.language).filter(Boolean)));
                  return languages.length > 0 ? languages.join(', ') : 'N/A';
                })()}
              </div>
            </div>
          </div>
        </div>

        {/* Question Submissions */}
        <div style={styles.section}>
          <div style={styles.sectionHeader}>
            <div style={styles.cardIcon}>💻</div>
            <h2 style={styles.sectionTitle}>Code Submissions</h2>
          </div>
          <div style={styles.sectionContent}>
            {submission.questionSubmissions.map((questionSub, index) => {
              // Find the corresponding question details from the assessment
              const questionDetails = submission.assessment?.questions?.find(q => q.id === questionSub.questionId);

              return (
              <div key={questionSub.questionId} style={styles.questionCard}>
                <div style={styles.questionHeader}>
                  <div style={styles.questionTitle}>
                    {questionDetails?.name || `Question ${index + 1}`}
                  </div>
                  {questionDetails?.description && (
                    <div style={{
                      fontSize: '14px',
                      color: '#464E57',
                      marginTop: '8px',
                      marginBottom: '12px',
                      lineHeight: '1.5'
                    }}>
                      {questionDetails.description}
                    </div>
                  )}
                  {questionDetails?.candidateInstruction && (
                    <div style={{
                      fontSize: '13px',
                      color: '#737980',
                      marginBottom: '12px',
                      padding: '8px 12px',
                      background: '#F8F9FB',
                      borderRadius: '4px',
                      borderLeft: '3px solid #099C73'
                    }}>
                      <strong>Instructions:</strong> {questionDetails.candidateInstruction}
                    </div>
                  )}
                  <div style={styles.questionScore}>
                    <span>Score: {questionSub.score}%</span>
                    <span>•</span>
                    <span>
                      {questionSub.testResults.filter(tr => tr.status === 'passed').length}/
                      {questionSub.testResults.length} tests passed
                    </span>
                  </div>
                </div>

                <div style={styles.sectionContent}>
                  <div style={{marginBottom: '24px'}}>
                    <div style={styles.codeHeader}>
                      <h4 style={{color: '#2d3748', margin: 0, fontSize: '16px', fontWeight: '600'}}>
                        Submitted Code
                      </h4>
                      <div style={{display: 'flex', alignItems: 'center', gap: '12px'}}>
                        <span style={{
                          background: 'linear-gradient(125.2deg, #099C73 8.04%, #015462 127.26%)',
                          color: 'white',
                          padding: '4px 12px',
                          borderRadius: '4px',
                          fontSize: '12px',
                          fontWeight: '600'
                        }}>
                          {questionSub.language}
                        </span>
                        <button
                          onClick={() => navigator.clipboard.writeText(questionSub.code)}
                          style={{
                            background: 'transparent',
                            border: '1px solid #DFE2E6',
                            borderRadius: '4px',
                            padding: '4px 8px',
                            cursor: 'pointer',
                            fontSize: '12px',
                            color: '#464E57'
                          }}
                          title="Copy code"
                        >
                          📋 Copy
                        </button>
                      </div>
                    </div>
                    <div style={styles.codeBlock}>
                      {questionSub.code}
                    </div>
                  </div>

                  <div>
                    <h4 style={{color: '#2d3748', marginBottom: '16px', fontSize: '16px', fontWeight: '600'}}>
                      Test Results
                    </h4>
                    <div style={{display: 'grid', gap: '12px'}}>
                      {questionSub.testResults.map((testResult, testIndex) => (
                        <div key={testIndex} style={{
                          ...styles.testResult,
                          ...(testResult.status === 'passed' ? styles.testPassed : styles.testFailed)
                        }}>
                          <div style={styles.testHeader}>
                            <div style={styles.testStatus}>
                              <span>{testResult.status === 'passed' ? '✅' : '❌'}</span>
                              <span>{testResult.status.toUpperCase()}</span>
                            </div>
                            <span style={{fontSize: '12px', opacity: 0.8}}>Test Case {testIndex + 1}</span>
                          </div>

                          <div style={styles.testDetails}>
                            <div style={styles.testDetailItem}>
                              <strong style={{display: 'block', marginBottom: '4px'}}>Input:</strong>
                              <pre style={{margin: 0, fontSize: '12px', fontFamily: 'Monaco, Consolas, monospace'}}>
                                {testResult.input}
                              </pre>
                            </div>

                            <div style={styles.testDetailItem}>
                              <strong style={{display: 'block', marginBottom: '4px'}}>Expected Output:</strong>
                              <pre style={{margin: 0, fontSize: '12px', fontFamily: 'Monaco, Consolas, monospace'}}>
                                {testResult.expectedOutput}
                              </pre>
                            </div>

                            {testResult.actualOutput && (
                              <div style={styles.testDetailItem}>
                                <strong style={{display: 'block', marginBottom: '4px'}}>Actual Output:</strong>
                                <pre style={{margin: 0, fontSize: '12px', fontFamily: 'Monaco, Consolas, monospace'}}>
                                  {testResult.actualOutput}
                                </pre>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
              );
            })}
          </div>
        </div>

        {/* Feedback Section */}
        <div style={styles.section}>
          <div style={styles.sectionHeader}>
            <div style={styles.cardIcon}>💬</div>
            <h2 style={styles.sectionTitle}>Recruiter Feedback</h2>
          </div>
          <div style={styles.sectionContent}>
            <textarea
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder="Share your thoughts on this submission... Consider commenting on code quality, problem-solving approach, test coverage, and overall performance."
              rows={6}
              style={styles.textarea}
              className="textarea"
            />
            <button
              onClick={handleSaveFeedback}
              style={styles.saveButton}
              className="save-button"
            >
              💾 Save Feedback
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TakeHomeSubmissionDetail;
